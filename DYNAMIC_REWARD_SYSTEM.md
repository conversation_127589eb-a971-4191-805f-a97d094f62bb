# Dynamic Reward System

## Overview

The Dynamic Reward System is a comprehensive solution for automatically rewarding staff based on their activities and achievements. It supports various reward types, automatic triggers, and detailed analytics.

## Features

### 🎯 **Dynamic Reward Triggers**

- **Innovation Ideas**: Reward staff for posting ideas
- **Comments**: Reward for commenting on innovation ideas
- **Likes**: Reward for liking innovation ideas
- **Package Confirmations**: Reward for booking packages
- **Package Referrals**: Reward for successful referrals
- **Game Completions**: Reward for game achievements
- **Milestones**: Reward for reaching specific milestones
- **Achievements**: Reward for unlocking achievements
- **Custom Events**: Support for custom reward triggers

### 💰 **Reward Types**

- **Cash**: Direct monetary rewards
- **Points**: Point-based rewards
- **Vouchers**: Meal voucher rewards
- **Percentage**: Percentage-based rewards

### 🎮 **Automatic Calculations**

- **Milestone Detection**: Automatically detect when staff reach milestones
- **Achievement Unlocking**: Reward first-time activities
- **Scheduled Processing**: Daily automatic reward calculations
- **Batch Processing**: Efficient bulk reward processing

### 📊 **Analytics & Reporting**

- **Dashboard**: Comprehensive reward analytics dashboard
- **Effectiveness Metrics**: Track reward system effectiveness
- **Staff Leaderboards**: Top earning staff rankings
- **Export Capabilities**: Export reward data for reporting

## Database Schema

### Core Models

#### Reward

```prisma
model Reward {
  id                            Int                           @id @default(autoincrement())
  name                          String                        @unique
  description                   String?
  eventType                     RewardEventType
  valueType                     RewardValueType
  value                         Float
  purpose                       RewardPurpose?
  isActive                      Boolean                       @default(true)
  locationId                    Int?
  conditions                    Json?
  maxRewardsPerUser             Int?
  maxRewardsPerDay              Int?
  maxRewardsPerUserPerDay       Int?
  validFrom                     DateTime?
  validUntil                    DateTime?
  metadata                      Json?
}
```

#### RewardRule

```prisma
model RewardRule {
  id                            Int                           @id @default(autoincrement())
  name                          String                        @unique
  description                   String?
  eventType                     RewardEventType
  isActive                      Boolean                       @default(true)
  rewardId                      Int
  conditions                    Json?
  priority                      Int                           @default(0)
  maxUsagePerUser               Int?
  maxUsagePerDay                Int?
  maxUsageTotal                 Int?
  currentUsageCount             Int                           @default(0)
  validFrom                     DateTime?
  validUntil                    DateTime?
  locationId                    Int?
}
```

#### RewardEventLog

```prisma
model RewardEventLog {
  id                            Int                           @id @default(autoincrement())
  staffId                       Int
  rewardId                      Int
  receivedAt                    DateTime                      @default(now())
  value                         Decimal
  valueType                     RewardValueType
  currency                      String?
  eventType                     RewardEventType
  eventData                     Json?
  transactionId                 String?
  entityType                    String?
  entityId                      Int?
}
```

### Enums

```prisma
enum RewardEventType {
  INNOVATION_IDEA_POSTED
  INNOVATION_IDEA_COMMENT
  INNOVATION_IDEA_LIKE
  PACKAGE_CONFIRMED
  PACKAGE_REFERRAL
  GAME_COMPLETION
  CUSTOM_EVENT
  ACHIEVEMENT_UNLOCKED
  MILESTONE_REACHED
}

enum RewardValueType {
  PERCENTAGE
  POINTS
  CASH
  VOUCHER
}

enum RewardPurpose {
  REFERRAL
  LOYALTY
  PROMOTION
  ACHIEVEMENT
  CONTRIBUTION
  INNOVATION
  ENGAGEMENT
  CUSTOM
}
```

## API Endpoints

### Reward Management

- `POST /api/v1/reward/dynamic/create` - Create a new reward
- `PATCH /api/v1/reward/dynamic/update` - Update a reward
- `PATCH /api/v1/reward/dynamic/toggle-status` - Activate/deactivate a reward
- `GET /api/v1/reward/dynamic/list` - List all rewards
- `DELETE /api/v1/reward/dynamic/:rewardId` - Delete a reward

### Reward Rules

- `POST /api/v1/reward/rules/create` - Create a reward rule
- `PATCH /api/v1/reward/rules/update` - Update a reward rule
- `PATCH /api/v1/reward/rules/toggle-status` - Activate/deactivate a rule
- `GET /api/v1/reward/rules/list` - List all reward rules
- `DELETE /api/v1/reward/rules/:ruleId` - Delete a reward rule

### Analytics

- `GET /api/v1/reward/analytics/dashboard` - Get reward dashboard data
- `GET /api/v1/reward/analytics/effectiveness` - Get effectiveness metrics
- `GET /api/v1/reward/analytics/leaderboard` - Get staff leaderboard
- `GET /api/v1/reward/analytics/export` - Export reward data

### Statistics

- `GET /api/v1/reward/statistics` - Get reward statistics
- `GET /api/v1/reward/staff/:targetStaffId/history` - Get staff reward history

### Automatic Processing

- `POST /api/v1/reward/automatic/milestones` - Process milestone rewards
- `POST /api/v1/reward/automatic/achievements` - Process achievement rewards
- `POST /api/v1/reward/automatic/run-all` - Run all automatic calculations

## Usage Examples

### Creating a Reward

```javascript
const rewardData = {
  name: 'Innovation Idea Reward',
  description: 'Reward for posting innovation ideas',
  eventType: 'INNOVATION_IDEA_POSTED',
  valueType: 'CASH',
  value: 500,
  purpose: 'INNOVATION',
  conditions: {
    requiredDepartments: ['IT', 'Innovation'],
    timeRestrictions: {
      allowedDays: [1, 2, 3, 4, 5], // Monday to Friday
    },
  },
  maxRewardsPerUser: 10,
  maxRewardsPerDay: 50,
};

const reward = await rewardService.createDynamicReward(staffId, rewardData);
```

### Creating a Reward Rule

```javascript
const ruleData = {
  name: 'First Innovation Idea Rule',
  description: 'Reward for first innovation idea',
  eventType: 'INNOVATION_IDEA_POSTED',
  rewardId: reward.id,
  priority: 10,
  maxUsagePerUser: 1, // Only once per user
  conditions: {
    achievementType: 'first_innovation_idea',
  },
};

const rule = await rewardService.createRewardRule(staffId, ruleData);
```

### Triggering Rewards

```javascript
// Automatically triggered when an innovation idea is posted
await rewardTriggers.onInnovationIdeaPosted(staffId, ideaId, locationId);

// Automatically triggered when a comment is added
await rewardTriggers.onInnovationIdeaComment(
  staffId,
  ideaId,
  commentId,
  locationId
);

// Automatically triggered when a package is confirmed
await rewardTriggers.onPackageConfirmed(
  staffId,
  packageId,
  bookingId,
  amount,
  locationId
);
```

## Configuration Examples

### Milestone Rewards

```javascript
// Reward for posting 10 innovation ideas
const milestoneReward = {
  name: 'Innovation Champion',
  eventType: 'MILESTONE_REACHED',
  valueType: 'CASH',
  value: 5000,
  conditions: {
    milestoneType: 'innovation_ideas_count',
    milestoneValue: 10,
  },
};
```

### Achievement Rewards

```javascript
// Reward for first innovation idea
const achievementReward = {
  name: 'First Idea Bonus',
  eventType: 'ACHIEVEMENT_UNLOCKED',
  valueType: 'CASH',
  value: 1000,
  conditions: {
    achievementType: 'first_innovation_idea',
  },
};
```

### Conditional Rewards

```javascript
// Reward only for specific departments during business hours
const conditionalReward = {
  name: 'Department Specific Reward',
  eventType: 'INNOVATION_IDEA_POSTED',
  valueType: 'POINTS',
  value: 100,
  conditions: {
    requiredDepartments: ['Research', 'Development'],
    timeRestrictions: {
      allowedHours: [9, 10, 11, 12, 13, 14, 15, 16, 17], // 9 AM to 5 PM
      allowedDays: [1, 2, 3, 4, 5], // Monday to Friday
    },
  },
};
```

## Scheduled Jobs

The system includes automatic processing that runs daily at 2:00 AM:

```javascript
// Start the automatic reward job
startAutomaticRewardJob();

// Manual trigger
const results = await triggerAutomaticRewardCalculation();
```

## Integration Points

### Innovation Services

- Innovation idea creation triggers rewards
- Comment creation triggers rewards
- Like creation triggers rewards

### Package Services

- Package confirmation triggers rewards
- Referral usage triggers rewards

### Game Services

- Game completion can trigger rewards
- Achievement unlocking triggers rewards

## Monitoring and Analytics

### Dashboard Metrics

- Total rewards distributed
- Total value distributed
- Active rewards and rules
- Recent reward activity
- Top performing rewards
- Top earning staff

### Effectiveness Tracking

- Engagement rates
- Reward distribution by type
- Innovation activity correlation
- Package booking correlation

## Best Practices

1. **Start Simple**: Begin with basic rewards and gradually add complexity
2. **Monitor Usage**: Regularly check analytics to ensure rewards are effective
3. **Set Limits**: Use usage limits to prevent abuse
4. **Test Conditions**: Thoroughly test reward conditions before activation
5. **Regular Review**: Periodically review and adjust reward values and conditions

## Troubleshooting

### Common Issues

1. **Rewards Not Triggering**

   - Check if reward rules are active
   - Verify conditions are met
   - Check usage limits

2. **Performance Issues**

   - Monitor automatic calculation job performance
   - Consider adjusting batch sizes
   - Review complex conditions

3. **Analytics Not Updating**
   - Ensure reward events are being logged
   - Check date filters in queries
   - Verify database connections

## Security Considerations

- All reward operations require appropriate permissions
- Reward calculations are logged for audit trails
- Usage limits prevent abuse
- Automatic calculations run with system privileges
