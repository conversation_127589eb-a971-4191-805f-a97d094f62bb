const { PrismaClient } = require('@prisma/client');

const db = new PrismaClient();

// Import the reward service (we'll simulate it here)
async function simulateRewardTrigger(eventType, staffId, entityType, entityId, contextAmount) {
  console.log(`\n🎯 Simulating reward trigger:`);
  console.log(`   Event Type: ${eventType}`);
  console.log(`   Staff ID: ${staffId}`);
  console.log(`   Entity: ${entityType}:${entityId}`);
  console.log(`   Amount: ₦${contextAmount}`);

  // Find active rewards for this event type where staff is eligible
  const rewards = await db.reward.findMany({
    where: {
      eventType,
      isActive: true,
      eligibleStaff: {
        some: {
          id: staffId,
        },
      },
    },
  });

  console.log(`   Found ${rewards.length} matching rewards`);

  if (rewards.length === 0) {
    console.log(`   ❌ No active rewards found for event type: ${eventType}`);
    return [];
  }

  const results = [];

  for (const reward of rewards) {
    console.log(`\n   Processing reward ID ${reward.id}:`);
    console.log(`     Value: ${reward.value}${reward.valueType === 'PERCENTAGE' ? '%' : ''}`);
    
    // Calculate actual reward value
    let actualRewardValue = reward.value;
    if (reward.valueType === 'PERCENTAGE') {
      if (!contextAmount) {
        console.log(`     ❌ Percentage reward requires contextAmount but none provided`);
        continue;
      }
      actualRewardValue = (reward.value / 100) * contextAmount;
    }

    console.log(`     Calculated reward: ₦${actualRewardValue}`);

    // Check if this reward was already given for this entity
    const existingLog = await db.rewardEventLog.findFirst({
      where: {
        staffId,
        rewardId: reward.id,
        entityType,
        entityId: parseInt(entityId) || entityId,
      },
    });

    if (existingLog) {
      console.log(`     ⚠️ Reward already given on ${existingLog.receivedAt}`);
      continue;
    }

    // Simulate giving the reward (don't actually create it, just show what would happen)
    console.log(`     ✅ Would give reward: ₦${actualRewardValue} to staff ${staffId}`);
    
    results.push({
      rewardId: reward.id,
      staffId,
      value: actualRewardValue,
      eventType,
      entityType,
      entityId
    });
  }

  return results;
}

async function testRewardTriggers() {
  try {
    console.log('🧪 Testing Reward Triggers for Completed Bookings...\n');

    // Get recent completed bookings with referral codes
    const completedBookings = await db.packageBooking.findMany({
      where: {
        bookingStatus: 'COMPLETED',
        referralCode: { not: null },
        createdAt: {
          gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
        }
      },
      include: {
        packages: {
          select: {
            name: true,
            bonusApplicable: true
          }
        }
      },
      orderBy: { createdAt: 'desc' },
      take: 5
    });

    console.log(`Found ${completedBookings.length} completed bookings with referral codes\n`);

    for (const booking of completedBookings) {
      console.log(`📦 Testing booking: ${booking.id}`);
      console.log(`   Package: ${booking.packages.name}`);
      console.log(`   Status: ${booking.bookingStatus}`);
      console.log(`   Referral Code: ${booking.referralCode}`);
      console.log(`   Bonus Applicable: ${booking.packages.bonusApplicable ? '✅' : '❌'}`);
      console.log(`   Amount: ₦${booking.totalAmount || booking.amount}`);

      if (!booking.packages.bonusApplicable) {
        console.log(`   ⚠️ Skipping - bonus not applicable for this package`);
        continue;
      }

      if (!booking.referralCode) {
        console.log(`   ⚠️ Skipping - no referral code`);
        continue;
      }

      // Find the referral code
      const referralCode = await db.referralCode.findUnique({
        where: { code: booking.referralCode },
        include: {
          assignedToStaff: {
            select: {
              id: true,
              fullName: true,
              isActive: true
            }
          }
        }
      });

      if (!referralCode) {
        console.log(`   ❌ Referral code not found: ${booking.referralCode}`);
        continue;
      }

      if (!referralCode.isActive) {
        console.log(`   ❌ Referral code is inactive`);
        continue;
      }

      if (!referralCode.assignedToStaff?.isActive) {
        console.log(`   ❌ Assigned staff is inactive`);
        continue;
      }

      console.log(`   👤 Assigned to: ${referralCode.assignedToStaff.fullName} (ID: ${referralCode.assignedToStaffId})`);

      // Simulate the reward trigger logic from completePackageBooking
      const amount = Number(booking.totalAmount || booking.amount);
      
      // Since this is a COMPLETED booking, it should trigger PACKAGE_REFERRAL_ONSITE
      const eventType = 'PACKAGE_REFERRAL_ONSITE';
      
      await simulateRewardTrigger(
        eventType,
        referralCode.assignedToStaffId,
        'package_booking',
        booking.id,
        amount
      );

      console.log(`\n${'='.repeat(80)}\n`);
    }

    // Also check if there are any PENDING bookings that should trigger online rewards
    console.log('\n🌐 Checking PENDING bookings for online rewards...\n');
    
    const pendingBookings = await db.packageBooking.findMany({
      where: {
        bookingStatus: 'PENDING',
        referralCode: { not: null },
        createdAt: {
          gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
        }
      },
      include: {
        packages: {
          select: {
            name: true,
            bonusApplicable: true
          }
        }
      },
      orderBy: { createdAt: 'desc' },
      take: 3
    });

    for (const booking of pendingBookings) {
      console.log(`📦 Testing PENDING booking: ${booking.id}`);
      
      if (booking.packages.bonusApplicable && booking.referralCode) {
        const referralCode = await db.referralCode.findUnique({
          where: { code: booking.referralCode }
        });

        if (referralCode?.isActive) {
          const amount = Number(booking.totalAmount || booking.amount);
          
          await simulateRewardTrigger(
            'PACKAGE_REFERRAL_ONLINE',
            referralCode.assignedToStaffId,
            'package_referral',
            booking.id,
            amount
          );
        }
      }
    }

  } catch (error) {
    console.error('❌ Error testing reward triggers:', error);
  } finally {
    await db.$disconnect();
  }
}

testRewardTriggers();
