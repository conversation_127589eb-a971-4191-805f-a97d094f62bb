import { db } from '../../utils/model';
import { HttpError } from '../../utils/httpError';
import { staffHasPermission, PERMISSIONS } from '../../utils/permission';
import { logger } from '../../utils/logger';
import { simpleTriggers } from '../reward/simpleTriggers';

export interface CreateCommentData {
  content: string;
  ideaId: number;
}

export interface UpdateCommentData {
  content: string;
}

export const innovationIdeaCommentService = {
  createComment: async (staffId: number, reqBody: CreateCommentData) => {
    try {
      // Check if staff is blacklisted for commenting
      const staff = await db.staff.findUnique({
        where: { id: staffId },
        include: { blacklist: true },
      });

      const hasActiveBlacklist = staff?.blacklist?.some(
        (bl) => bl.action === 'INNOVATION_IDEA_COMMENT' && bl.isActive
      );

      if (hasActiveBlacklist) {
        throw new HttpError(
          'You are restricted from commenting on innovation ideas. Kindly reach out to the administrator.',
          403
        );
      }

      const { content, ideaId } = reqBody;

      if (!content || content.trim().length === 0) {
        throw new HttpError('Comment content is required', 400);
      }

      // Check if the innovation idea exists
      const idea = await db.innovationIdea.findUnique({
        where: { id: ideaId },
      });

      if (!idea) {
        throw new HttpError('Innovation idea not found', 404);
      }

      const comment = await db.innovationIdeaComment.create({
        data: {
          content,
          staffId,
          ideaId,
        },
      });

      if (Number(idea.authorId) !== Number(staffId)) {
        try {
          await simpleTriggers.onInnovationComment(staffId, comment.id, ideaId);
        } catch (rewardError) {
          logger.error(
            'Error triggering innovation comment reward:',
            rewardError
          );
          // Don't fail the main operation if reward fails
        }
      }

      return {
        message: 'Comment created successfully',
      };
    } catch (error) {
      logger.error('Error creating comment:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to create comment', 400);
    }
  },

  deleteComment: async (staffId: number, commentId: number) => {
    try {
      await staffHasPermission(staffId, PERMISSIONS.HUB_EDIT);
      const comment = await db.innovationIdeaComment.findUnique({
        where: { id: commentId },
      });

      if (!comment) {
        throw new HttpError('Comment not found', 404);
      }

      await db.innovationIdeaComment.delete({
        where: { id: commentId },
      });

      return {
        message: 'Comment deleted successfully',
      };
    } catch (error) {
      logger.error('Error deleting comment:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to delete comment', 400);
    }
  },
};
