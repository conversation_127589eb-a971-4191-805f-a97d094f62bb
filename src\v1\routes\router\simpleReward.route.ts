import { Router } from 'express';
import { secure } from '../../middleware/auth';
import { simpleRewardControllers } from '../../controllers/reward/simpleReward.controller';

const simpleRewardRoute = Router();

/**
 * Simple Reward Routes
 * Clean, intuitive API endpoints that match your actual needs
 */

// Core Reward Management (Admin)
simpleRewardRoute.get(
  '/list',
  secure,
  simpleRewardControllers.ListRewardsHandler
);
simpleRewardRoute.post(
  '/create',
  secure,
  simpleRewardControllers.CreateRewardHandler
);
simpleRewardRoute.patch(
  '/update',
  secure,
  simpleRewardControllers.UpdateRewardHandler
);
simpleRewardRoute.delete(
  '/:rewardId',
  secure,
  simpleRewardControllers.DeleteRewardHandler
);

// History and Analytics
simpleRewardRoute.get(
  '/history',
  secure,
  simpleRewardControllers.GetRewardHistoryHandler
);
simpleRewardRoute.get(
  '/statistics',
  secure,
  simpleRewardControllers.GetRewardStatisticsHandler
);

// Manual Reward Triggering (for testing/admin purposes)
simpleRewardRoute.post(
  '/trigger/innovation-idea',
  secure,
  simpleRewardControllers.TriggerInnovationIdeaRewardHandler
);
simpleRewardRoute.post(
  '/trigger/innovation-comment',
  secure,
  simpleRewardControllers.TriggerInnovationCommentRewardHandler
);
simpleRewardRoute.post(
  '/trigger/package-referral',
  secure,
  simpleRewardControllers.TriggerPackageReferralRewardHandler
);

// Utilities
simpleRewardRoute.get(
  '/preview',
  secure,
  simpleRewardControllers.PreviewRewardsHandler
);
simpleRewardRoute.post(
  '/referral/validate',
  simpleRewardControllers.VerifyReferralCodeHandler
);

export { simpleRewardRoute };
