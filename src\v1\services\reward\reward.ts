import { db } from '../../utils/model';
import { HttpError } from '../../utils/httpError';
import { formatString } from '../../utils/stringFormatter';
import { logger } from '../../utils/logger';
import { simpleRewardService } from './simpleReward';

export const rewardService = {
  verifyReferralCode: async (reqBody: any) => {
    try {
      const refCode = formatString.formatUpperCase(reqBody.code);
      const checkStaffCode = await db.referralCode.findUnique({
        where: { code: refCode },
      });
      if (!checkStaffCode) {
        throw new HttpError('Referral code does not exist', 400);
      }

      if (!checkStaffCode.isActive) {
        throw new HttpError('Referral code has been deactivated', 400);
      }

      return { message: 'Code still active' };
    } catch (error) {
      logger.error('Failed to verify referral code', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to verify referral code', 400);
    }
  },

  // Simple Reward Management (clean and straightforward)
  createReward: simpleRewardService.createReward,
  updateReward: simpleRewardService.updateReward,
  deleteReward: simpleRewardService.deleteReward,
  getAllRewards: simpleRewardService.listRewards,

  // Statistics and History
  getRewardStatistics: simpleRewardService.getRewardStatistics,
  getRewardHistory: simpleRewardService.getRewardHistory,
};
