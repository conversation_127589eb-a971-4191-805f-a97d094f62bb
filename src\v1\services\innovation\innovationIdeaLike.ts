import { db } from '../../utils/model';
import { HttpError } from '../../utils/httpError';
import { logger } from '../../utils/logger';
import { simpleTriggers } from '../reward/simpleTriggers';

export const innovationIdeaLikeService = {
  toggleLike: async (staffId: number, ideaId: number) => {
    try {
      // Check if staff is blacklisted for liking
      const staff = await db.staff.findUnique({
        where: { id: staffId },
        include: { blacklist: true },
      });

      const hasActiveBlacklist = staff?.blacklist?.some(
        (bl) => bl.action === 'INNOVATION_IDEA_LIKE' && bl.isActive
      );

      if (hasActiveBlacklist) {
        throw new HttpError(
          'You are restricted from liking innovation ideas. Kindly reach out to the administrator.',
          403
        );
      }

      const idea = await db.innovationIdea.findUnique({
        where: { id: ideaId },
      });

      if (!idea) {
        throw new HttpError('Innovation idea not found', 404);
      }

      const existingLike = await db.innovationIdeaLike.findUnique({
        where: {
          staffId_ideaId: {
            staffId,
            ideaId,
          },
        },
      });

      if (existingLike) {
        const wasLiked = existingLike.liked;

        await db.innovationIdeaLike.update({
          where: {
            id: existingLike.id,
          },
          data: {
            liked: !existingLike.liked,
          },
        });

        // Handle rewards only if not the author
        if (idea.authorId !== staffId) {
          try {
            if (wasLiked) {
              // Was liked, now unliked - remove reward
              await simpleTriggers.onInnovationUnlike(
                staffId,
                existingLike.id,
                ideaId
              );
            } else {
              // Was unliked, now liked - give reward
              await simpleTriggers.onInnovationLike(
                staffId,
                existingLike.id,
                ideaId
              );
            }
          } catch (rewardError) {
            logger.error('Error handling innovation like reward:', rewardError);
          }
        }

        return {
          message: wasLiked
            ? 'Innovation idea unliked successfully'
            : 'Innovation idea liked successfully',
        };
      } else {
        const like = await db.innovationIdeaLike.create({
          data: {
            staffId,
            ideaId,
          },
        });

        // Trigger reward only if not the author
        if (idea.authorId !== staffId) {
          try {
            await simpleTriggers.onInnovationLike(staffId, like.id, ideaId);
          } catch (rewardError) {
            logger.error(
              'Error triggering innovation like reward:',
              rewardError
            );
          }
        }

        return {
          message: 'Innovation idea liked successfully',
        };
      }
    } catch (error) {
      logger.error('Error toggling like on innovation idea:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to toggle like on innovation idea', 500);
    }
  },
};
