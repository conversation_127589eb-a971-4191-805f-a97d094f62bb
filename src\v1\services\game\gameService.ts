import { PrismaClient, GameType, GameStatus } from '@prisma/client';
import { logger } from '../../utils/logger';
import { sendToGame, notifyGameUpdate, broadcast, sendToUser } from '../socket';
import { GameNotificationService } from '../notification/gameNotificationService';
import { getMainSystemAccount } from '../system';

const prisma = new PrismaClient();

export interface CreateGameData {
  title: string;
  description: string;
  gameType: GameType;
  startDate: Date;
  endDate: Date;
  maxParticipants?: number;
  entryFee?: number;
  prizePool: number;
  rules?: string;
  createdById: number;
  locationId?: number;
  groupId?: number;
  questions: {
    question: string;
    questionType:
      | 'MULTIPLE_CHOICE'
      | 'TRUE_FALSE'
      | 'SHORT_ANSWER'
      | 'ESSAY'
      | 'RATING';
    options?: any;
    correctAnswer?: string;
    points?: number;
    timeLimit?: number;
    order: number;
  }[];
  rewards: {
    position: number;
    rewardType: 'CASH' | 'POINTS' | 'VOUCHER' | 'BADGE' | 'CERTIFICATE';
    amount: number;
    description?: string;
  }[];
  attachments?: {
    fileName: string;
    fileUrl: string;
    fileType: string;
    fileSize: number;
  }[];
}

export interface UpdateGameData {
  title?: string;
  description?: string;
  status?: GameStatus;
  startDate?: Date;
  endDate?: Date;
  maxParticipants?: number;
  entryFee?: number;
  prizePool?: number;
  rules?: string;
}

export class GameService {
  // Create a new game
  static async createGame(data: CreateGameData) {
    try {
      // Validate dates
      if (data.startDate >= data.endDate) {
        throw new Error('Start date must be before end date');
      }

      if (data.startDate <= new Date()) {
        throw new Error('Start date must be in the future');
      }

      // Create game with questions and rewards
      const game = await prisma.game.create({
        data: {
          title: data.title,
          description: data.description,
          gameType: data.gameType,
          startDate: data.startDate,
          endDate: data.endDate,
          maxParticipants: data.maxParticipants,
          entryFee: data.entryFee || 0,
          prizePool: data.prizePool,
          rules: data.rules,
          createdById: data.createdById,
          locationId: data.locationId,
          groupId: data.groupId,
          questions: {
            create: data.questions.map((q) => ({
              question: q.question,
              questionType: q.questionType,
              options: q.options,
              correctAnswer: q.correctAnswer,
              points: q.points || 1,
              timeLimit: q.timeLimit,
              order: q.order,
            })),
          },
          rewards: {
            create: data.rewards.map((r) => ({
              position: r.position,
              rewardType: r.rewardType,
              amount: r.amount,
              description: r.description,
            })),
          },
          attachments: data.attachments
            ? {
                create: data.attachments.map((a) => ({
                  fileName: a.fileName,
                  fileUrl: a.fileUrl,
                  fileType: a.fileType,
                  fileSize: a.fileSize,
                })),
              }
            : undefined,
        },
        include: {
          createdBy: {
            select: {
              id: true,
              fullName: true,
            },
          },
          location: {
            select: {
              id: true,
              name: true,
            },
          },
          group: {
            select: {
              id: true,
              name: true,
            },
          },
          questions: {
            orderBy: { order: 'asc' },
          },
          rewards: {
            orderBy: { position: 'asc' },
          },
          attachments: true,
          _count: {
            select: {
              participants: true,
            },
          },
        },
      });

      logger.info(`Game created: ${game.id} by user ${data.createdById}`);
      return game;
    } catch (error) {
      logger.error('Error creating game:', error);
      throw error;
    }
  }

  // Get game by ID
  static async getGameById(gameId: string, userId?: number) {
    try {
      const game = await prisma.game.findUnique({
        where: { id: gameId },
        include: {
          createdBy: {
            select: {
              id: true,
              fullName: true,
            },
          },
          location: {
            select: {
              id: true,
              name: true,
            },
          },
          group: {
            select: {
              id: true,
              name: true,
            },
          },
          questions: {
            select: {
              id: true,
              question: true,
              questionType: true,
              options: true,
              points: true,
              timeLimit: true,
              order: true,
              // Don't include correct answer unless user is creator
              ...(userId && {
                correctAnswer: true,
              }),
            },
            orderBy: { order: 'asc' },
          },
          rewards: {
            orderBy: { position: 'asc' },
          },
          participants: {
            include: {
              staff: {
                select: {
                  id: true,
                  fullName: true,
                },
              },
            },
            orderBy: { joinedAt: 'asc' },
          },
          leaderboard: {
            include: {
              staff: {
                select: {
                  id: true,
                  fullName: true,
                },
              },
            },
            orderBy: { position: 'asc' },
          },
          attachments: true,
          _count: {
            select: {
              participants: true,
              questions: true,
            },
          },
        },
      });

      if (!game) {
        throw new Error('Game not found');
      }

      // Hide correct answers unless user is the creator or game is completed
      if (userId !== game.createdById && game.status !== 'COMPLETED') {
        game.questions = game.questions.map((q) => ({
          ...q,
          correctAnswer: null,
        }));
      }

      return game;
    } catch (error) {
      logger.error('Error getting game:', error);
      throw error;
    }
  }

  // Get all games with filters
  static async getGames(
    filters: {
      status?: GameStatus;
      gameType?: GameType;
      locationId?: number;
      groupId?: number;
      createdById?: number;
      page?: number;
      limit?: number;
    } = {}
  ) {
    try {
      const {
        status,
        gameType,
        locationId,
        groupId,
        createdById,
        page = 1,
        limit = 20,
      } = filters;

      const skip = (page - 1) * limit;

      const where: any = {};
      if (status) where.status = status;
      if (gameType) where.gameType = gameType;
      if (locationId) where.locationId = locationId;
      if (groupId) where.groupId = groupId;
      if (createdById) where.createdById = createdById;

      const [games, total] = await Promise.all([
        prisma.game.findMany({
          where,
          include: {
            createdBy: {
              select: {
                id: true,
                fullName: true,
              },
            },
            location: {
              select: {
                id: true,
                name: true,
              },
            },
            group: {
              select: {
                id: true,
                name: true,
              },
            },
            _count: {
              select: {
                participants: true,
                questions: true,
              },
            },
          },
          orderBy: { createdAt: 'desc' },
          skip,
          take: limit,
        }),
        prisma.game.count({ where }),
      ]);

      return {
        games,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      logger.error('Error getting games:', error);
      throw error;
    }
  }

  // Update game
  static async updateGame(
    gameId: string,
    data: UpdateGameData,
    userId: number
  ) {
    try {
      // Check if user is the creator
      const existingGame = await prisma.game.findFirst({
        where: {
          id: gameId,
          createdById: userId,
        },
      });

      if (!existingGame) {
        throw new Error('Game not found or you are not the creator');
      }

      // Don't allow updates if game is active or completed
      if (
        existingGame.status === 'ACTIVE' ||
        existingGame.status === 'COMPLETED'
      ) {
        throw new Error('Cannot update active or completed games');
      }

      const updatedGame = await prisma.game.update({
        where: { id: gameId },
        data,
        include: {
          createdBy: {
            select: {
              id: true,
              fullName: true,
            },
          },
          _count: {
            select: {
              participants: true,
              questions: true,
            },
          },
        },
      });

      // Notify participants about game update
      notifyGameUpdate(gameId, 'game_updated', {
        updatedBy: userId,
        changes: data,
      });

      logger.info(`Game updated: ${gameId} by user ${userId}`);
      return updatedGame;
    } catch (error) {
      logger.error('Error updating game:', error);
      throw error;
    }
  }

  // Publish game (make it available for participation)
  static async publishGame(gameId: string, userId: number) {
    try {
      const game = await prisma.game.findFirst({
        where: {
          id: gameId,
          createdById: userId,
          status: 'DRAFT',
        },
        include: {
          questions: true,
          rewards: true,
        },
      });

      if (!game) {
        throw new Error(
          'Game not found, you are not the creator, or game is not in draft status'
        );
      }

      // Validate game has questions and rewards
      if (game.questions.length === 0) {
        throw new Error('Game must have at least one question');
      }

      if (game.rewards.length === 0) {
        throw new Error('Game must have at least one reward');
      }

      const updatedGame = await prisma.game.update({
        where: { id: gameId },
        data: { status: 'PUBLISHED' },
        include: {
          createdBy: {
            select: {
              id: true,
              fullName: true,
            },
          },
          _count: {
            select: {
              participants: true,
              questions: true,
            },
          },
        },
      });

      // Get creator details for notification
      const creator = await prisma.staff.findUnique({
        where: { id: userId },
        select: { fullName: true },
      });

      // Notify about game publication using notification service
      await GameNotificationService.notifyGameCreated(
        gameId,
        updatedGame.title,
        creator?.fullName || 'Unknown',
        updatedGame.locationId || undefined
      );

      // Emit socket notification for game published
      if (updatedGame.locationId) {
        broadcast('game_published', {
          gameId,
          title: updatedGame.title,
          gameType: updatedGame.gameType,
          startDate: updatedGame.startDate,
          endDate: updatedGame.endDate,
          prizePool: updatedGame.prizePool,
          createdBy: creator?.fullName || 'Unknown',
          locationId: updatedGame.locationId,
          timestamp: new Date(),
        });
      }

      logger.info(`Game published: ${gameId} by user ${userId}`);
      return updatedGame;
    } catch (error) {
      logger.error('Error publishing game:', error);
      throw error;
    }
  }

  // Start game (activate it)
  static async startGame(gameId: string, userId: number) {
    try {
      const game = await prisma.game.findFirst({
        where: {
          id: gameId,
          createdById: userId,
          status: 'PUBLISHED',
        },
      });

      if (!game) {
        throw new Error(
          'Game not found, you are not the creator, or game is not published'
        );
      }

      // Check if start date has passed
      if (new Date() < game.startDate) {
        throw new Error(
          'Game cannot be started before the scheduled start date'
        );
      }

      const updatedGame = await prisma.game.update({
        where: { id: gameId },
        data: { status: 'ACTIVE' },
      });

      // Notify participants about game start using notification service
      await GameNotificationService.notifyGameStarted(gameId);

      logger.info(`Game started: ${gameId} by user ${userId}`);
      return updatedGame;
    } catch (error) {
      logger.error('Error starting game:', error);
      throw error;
    }
  }

  // Complete game
  static async completeGame(gameId: string, userId: number) {
    try {
      const game = await prisma.game.findFirst({
        where: {
          id: gameId,
          createdById: userId,
          status: 'ACTIVE',
        },
      });

      if (!game) {
        throw new Error(
          'Game not found, you are not the creator, or game is not active'
        );
      }

      // Update leaderboard and complete game
      await this.updateLeaderboard(gameId);

      const updatedGame = await prisma.game.update({
        where: { id: gameId },
        data: { status: 'COMPLETED' },
      });

      // Distribute rewards
      const rewardDistributions = await this.distributeRewards(gameId);

      // Notify participants about game completion using notification service
      await GameNotificationService.notifyGameCompleted(gameId);

      // Emit socket notification for game completion with rewards
      sendToGame(gameId, 'game_completed_with_rewards', {
        gameId,
        completedAt: new Date(),
        rewardDistributions,
        timestamp: new Date(),
      });

      logger.info(`Game completed: ${gameId} by user ${userId}`);
      return updatedGame;
    } catch (error) {
      logger.error('Error completing game:', error);
      throw error;
    }
  }

  // Cancel game
  static async cancelGame(gameId: string, userId: number, reason?: string) {
    try {
      const game = await prisma.game.findFirst({
        where: {
          id: gameId,
          createdById: userId,
          status: { in: ['DRAFT', 'PUBLISHED', 'ACTIVE'] },
        },
      });

      if (!game) {
        throw new Error(
          'Game not found, you are not the creator, or game cannot be cancelled'
        );
      }

      const updatedGame = await prisma.game.update({
        where: { id: gameId },
        data: { status: 'CANCELLED' },
      });

      // Notify participants about game cancellation
      sendToGame(gameId, 'game_cancelled', {
        gameId,
        cancelledBy: userId,
        reason,
        cancelledAt: new Date(),
      });

      logger.info(
        `Game cancelled: ${gameId} by user ${userId}. Reason: ${reason || 'No reason provided'}`
      );
      return updatedGame;
    } catch (error) {
      logger.error('Error cancelling game:', error);
      throw error;
    }
  }

  // Update leaderboard
  static async updateLeaderboard(gameId: string) {
    try {
      // Get all participants with their scores
      const participants = await prisma.gameParticipant.findMany({
        where: {
          gameId,
          status: 'COMPLETED',
        },
        include: {
          staff: {
            select: {
              id: true,
              fullName: true,
            },
          },
        },
        orderBy: [
          { score: 'desc' },
          { completedAt: 'asc' }, // Earlier completion time as tiebreaker
        ],
      });

      // Clear existing leaderboard
      await prisma.gameLeaderboard.deleteMany({
        where: { gameId },
      });

      // Create new leaderboard entries
      const leaderboardEntries = participants.map((participant, index) => ({
        gameId,
        staffId: participant.staffId,
        position: index + 1,
        score: participant.score,
        completionTime: participant.completedAt,
      }));

      if (leaderboardEntries.length > 0) {
        await prisma.gameLeaderboard.createMany({
          data: leaderboardEntries,
        });
      }

      // Notify about leaderboard update
      sendToGame(gameId, 'leaderboard_updated', {
        gameId,
        leaderboard: leaderboardEntries,
        updatedAt: new Date(),
      });

      logger.info(`Leaderboard updated for game: ${gameId}`);
      return leaderboardEntries;
    } catch (error) {
      logger.error('Error updating leaderboard:', error);
      throw error;
    }
  }

  // Distribute rewards
  static async distributeRewards(gameId: string) {
    try {
      const game = await prisma.game.findUnique({
        where: { id: gameId },
        include: {
          rewards: {
            orderBy: { position: 'asc' },
          },
          leaderboard: {
            include: {
              staff: { include: { account: true } },
            },
            orderBy: { position: 'asc' },
          },
        },
      });

      if (!game) {
        throw new Error('Game not found');
      }

      const rewardDistributions = [];

      for (const reward of game.rewards) {
        const winner = game.leaderboard.find(
          (entry) => entry.position === reward.position
        );

        if (winner && reward.rewardType === 'CASH') {
          // Create transaction for cash reward
          const mainSystemAccount = await getMainSystemAccount();

          const transaction = await prisma.transaction.create({
            data: {
              amount: reward.amount,
              type: 'REWARD',
              reference: `GAME_REWARD_${gameId}_${winner.staffId}_${Date.now()}`,
              mode: 'internal',
              role: 'STAFF',
              remarks: `Game reward - ${game.title} (Position ${reward.position})`,
              fromAccountId: mainSystemAccount?.account?.id,
              toAccountId: winner.staff.account?.id,
              locationId: game.locationId,
            },
          });

          // Update reward as awarded
          await prisma.gameReward.update({
            where: { id: reward.id },
            data: {
              isAwarded: true,
              awardedToStaffId: winner.staffId,
              awardedAt: new Date(),
              transactionId: transaction.id,
            },
          });

          rewardDistributions.push({
            rewardId: reward.id,
            winnerId: winner.staffId,
            amount: reward.amount,
            transactionId: transaction.id,
          });

          // Notify winner about their reward
          await GameNotificationService.notifyRewardDistributed(
            winner.staffId,
            game.title,
            Number(reward.amount),
            reward.position
          );

          // Emit socket notification to winner
          sendToUser(winner.staffId, 'reward_won', {
            gameId,
            gameTitle: game.title,
            position: reward.position,
            amount: reward.amount,
            rewardType: 'CASH',
            transactionId: transaction.id,
            timestamp: new Date(),
          });

          logger.info(
            `Cash reward distributed: ${reward.amount} to user ${winner.staffId} for game ${gameId}`
          );
        }
      }

      return rewardDistributions;
    } catch (error) {
      logger.error('Error distributing rewards:', error);
      throw error;
    }
  }

  // Delete game (only if draft and no participants)
  static async deleteGame(gameId: string, userId: number) {
    try {
      const game = await prisma.game.findFirst({
        where: {
          id: gameId,
          createdById: userId,
          status: 'DRAFT',
        },
        include: {
          _count: {
            select: {
              participants: true,
            },
          },
        },
      });

      if (!game) {
        throw new Error(
          'Game not found, you are not the creator, or game is not in draft status'
        );
      }

      if (game._count.participants > 0) {
        throw new Error('Cannot delete game with participants');
      }

      await prisma.game.delete({
        where: { id: gameId },
      });

      logger.info(`Game deleted: ${gameId} by user ${userId}`);
      return { success: true };
    } catch (error) {
      logger.error('Error deleting game:', error);
      throw error;
    }
  }
}
