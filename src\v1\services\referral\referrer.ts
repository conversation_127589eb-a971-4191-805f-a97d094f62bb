import * as bcrypt from 'bcryptjs';
import { db } from '../../utils/model';
import { HttpError } from '../../utils/httpError';
import jwt, { Secret } from 'jsonwebtoken';
import { formatString } from '../../utils/stringFormatter';
import config from '../../../config/app.config';
import {
  getCache,
  setCache,
  deleteCache,
  deleteCacheByPattern,
} from '../../utils/cache';
import { devLog } from '../../utils/logger';
import { staffHasPermission, PERMISSIONS } from '../../utils/permission';
import { createDateFilter } from '../../utils/util';
import { enqueueSendEmailJob } from '../../jobs/queueJobs/queues/emailQueueJob';
import { logger } from '../../utils/logger';

export const SECRET_KEY = config.SIGNING_TOKEN_SECRET as Secret;

// Helper function to clear all admin-related caches
export const clearAdminCaches = async (): Promise<void> => {
  await deleteCacheByPattern('referral:*');
};

export const referrerService = {
  // Get all referrals with pagination, search, and caching
  getAllReferrals: async (staffId: any, query: any = {}) => {
    try {
      await staffHasPermission(staffId, PERMISSIONS.REFERRAL_VIEW);

      const page: number = parseInt(query.page as string) || 1;
      const limit: number = parseInt(query.limit as string) || 10;
      const search: string = (query.search as string) || '';
      const status = query.status;
      const startDate = query.startDate as string;
      const endDate = query.endDate as string;
      const dateFilter = createDateFilter(startDate, endDate);

      const whereClause: any = {
        ...(search
          ? {
              OR: [
                { patientFirstName: { contains: search } },
                { patientLastName: { contains: search } },
                { patientPhoneNumber: { contains: search } },
                { patientEmail: { contains: search } },
              ],
            }
          : {}),
        ...(status ? { status: status.toUpperCase() } : {}),
        ...dateFilter,
      };

      const [referrals, totalCount] = await db.$transaction([
        db.externalReferral.findMany({
          where: whereClause,
          orderBy: { createdAt: 'desc' },
          skip: (page - 1) * limit,
          take: limit,
          include: {
            internalDoctor: {
              select: {
                fullName: true,
              },
            },
          },
        }),
        db.externalReferral.count({
          where: whereClause,
        }),
      ]);

      const response = {
        referrals: referrals,
        totalPages: Math.ceil(totalCount / limit),
        totalCount: totalCount,
        currentPage: page,
        limit: limit,
      };

      return response;
    } catch (error) {
      logger.error('Failed to get all referrals', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to get all referrals', 400);
    }
  },

  getReferrerProfile: async (referrerId: string) => {
    try {
      const cacheKey = `referrer:profile:${referrerId}`;

      // const cachedProfile = await getCache(cacheKey);
      // if (cachedProfile) {
      //   devLog(`Referrer profile for ${referrerId} retrieved from cache`);
      //   return cachedProfile;
      // }

      const result = await db.referringEntity.findUnique({
        where: { id: referrerId },
        include: {
          submittedCredentials: true,
        },
      });

      if (!result) {
        throw new HttpError('Referrer account not found', 404);
      }

      const { id, password, ...rest } = result;

      await setCache(cacheKey, rest);

      return rest;
    } catch (error) {
      logger.error('Failed to get referrer profile', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to get referrer profile', 400);
    }
  },

  //Register a new account
  registerAccount: async (reqBody: any) => {
    try {
      const { email, name, type, phone, ...rest } = reqBody;
      const newEmail = formatString.formatEmail(email);

      const existingAccount = await db.referringEntity.findUnique({
        where: { email: newEmail },
      });
      if (existingAccount) {
        throw new HttpError('Account with the email already exists', 409);
      }
      await db.referringEntity.create({
        data: {
          ...rest,
          name,
          phone,
          email: newEmail,
          entityType:
            type === 'organization' ? 'ORGANIZATION' : 'INDIVIDUAL_DOCTOR',
        },
      });
      return { message: 'Account created succesfully' };
    } catch (error) {
      logger.error('Failed to register account', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to register account', 400);
    }
  },

  //Confirm account
  confirmAccount: async (staffId: any, reqBody: any) => {
    try {
      await staffHasPermission(staffId, PERMISSIONS.REFERRAL_EDIT);
      const { email } = reqBody;
      const newEmail = formatString.formatEmail(email);

      const existingAccount = await db.referringEntity.findUnique({
        where: { email: newEmail },
      });
      if (!existingAccount) {
        throw new HttpError('Account does not exist', 400);
      }
      if (existingAccount.activated) {
        throw new HttpError('Account already confirmed', 400);
      }
      await db.referringEntity.update({
        where: { id: existingAccount.id },
        data: {
          activated: true,
        },
      });
      const confirmURL = `${config.REFERRAL_BASE_URL}/login/confirm?q=${existingAccount.id}`;
      const referralLOGIN = `${config.REFERRAL_BASE_URL}/login`;
      // Send welcome email
      const mailOptions = {
        from: '"Cedarcrest Hospitals Innovations" <<EMAIL>>',
        to: newEmail,
        subject: 'Welcome to Cedarcrest Hospitals Referral System',
        template: 'referral',
        context: {
          name: existingAccount.name,
          url: confirmURL,
          login: referralLOGIN,
        },
      };
      enqueueSendEmailJob(mailOptions);

      return { message: 'Account confirmed succesfully' };
    } catch (error) {
      logger.error('Failed to confirm account', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to confirm account', 400);
    }
  },

  validateAccount: async (validateAccount: string) => {
    try {
      let user = await db.referringEntity.findUnique({
        where: { id: validateAccount },
      });
      if (!user) {
        throw new HttpError('Account does not exist', 400);
      }

      if (user.isEmailValidated) {
        throw new HttpError('Account already validated', 400);
      }
      return {
        message: 'User email verified successfully',
      };
    } catch (error) {
      logger.error('Failed to validate account', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to validate account', 400);
    }
  },

  setPassword: async (reqBody: any) => {
    try {
      const { id, password } = reqBody;
      let user = await db.referringEntity.findUnique({
        where: { id: id },
      });
      if (!user) {
        throw new HttpError('Account does not exist', 400);
      }

      if (user.isEmailValidated) {
        throw new HttpError('Account already validated', 400);
      }
      const hashedPassword = await bcrypt.hash(password, 10);
      user = await db.referringEntity.update({
        where: {
          id: user.id,
        },
        data: {
          isEmailValidated: true,
          dateEmailValidated: new Date(),
          activated: true,
          isActive: true,
          password: hashedPassword,
        },
      });
      return {
        message: 'Password set successfully',
      };
    } catch (error) {
      logger.error('Failed to set password', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to set password', 400);
    }
  },

  loginAccount: async (reqBody: any) => {
    try {
      const { email, password } = reqBody;
      const newEmail = formatString.formatEmail(email);
      let account = await db.referringEntity.findUnique({
        where: { email: newEmail },
      });
      if (!account) {
        throw new HttpError('Account cannot be found', 400);
      }
      if (!account.activated) {
        throw new HttpError('Account is not activated yet', 400);
      }
      if (!account.isActive) {
        throw new HttpError('Account is deactivated', 400);
      }
      const userPassword = account.password ? account.password : '';
      const isPasswordValid = await bcrypt.compare(password, userPassword);
      if (isPasswordValid) {
        const token = jwt.sign({ id: account.id }, SECRET_KEY, {
          expiresIn: '7 days',
        });
        await db.referringEntity.update({
          where: { id: account.id },
          data: { lastLogin: new Date() },
        });
        return {
          id: account.id,
          token: token,
        };
      } else {
        throw new HttpError('Enter a valid password', 400);
      }
    } catch (error) {
      logger.error('Failed to login account', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to login account', 400);
    }
  },

  getProfile: async (accountId: string) => {
    try {
      const cacheKey = `referrer:profile:${accountId}`;

      // const cachedProfile = await getCache(cacheKey);
      // if (cachedProfile) {
      //   devLog(`Referrer profile for ${accountId} retrieved from cache`);
      //   return cachedProfile;
      // }

      const result = await db.referringEntity.findUnique({
        where: { id: accountId },
        // select: {
        //   isActive: true,
        //   email: true,
        //   entityType: true,
        //   id: true,
        //   name: true,
        // },
      });

      if (!result) {
        throw new HttpError('Account not found', 404);
      }

      const {
        password,
        rewardId,
        medicalLicenseNumber,
        resetPasswordToken,
        dateResetPasswordRequest,
        dateEmailValidated,
        isEmailValidated,
        activated,
        createdAt,
        updatedAt,
        ...rest
      } = result;

      // await setCache(cacheKey, rest);

      return rest;
    } catch (error) {
      logger.error('Failed to get profile', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to get profile', 400);
    }
  },
};
