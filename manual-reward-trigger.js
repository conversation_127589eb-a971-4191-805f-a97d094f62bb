const { PrismaClient } = require('@prisma/client');

const db = new PrismaClient();

// Import the actual reward service functions
async function triggerReward(eventType, staffId, entityType, entityId, contextAmount) {
  console.log(`\n🎯 Triggering reward:`);
  console.log(`   Event Type: ${eventType}`);
  console.log(`   Staff ID: ${staffId}`);
  console.log(`   Entity: ${entityType}:${entityId}`);
  console.log(`   Amount: ₦${contextAmount}`);

  try {
    const staff = await db.staff.findUnique({
      where: { id: staffId },
      select: { locationId: true, fullName: true },
    });

    if (!staff) {
      console.log(`   ❌ Staff ${staffId} not found`);
      return [];
    }

    // Find active rewards for this event type where staff is eligible
    const rewards = await db.reward.findMany({
      where: {
        eventType,
        isActive: true,
        eligibleStaff: {
          some: {
            id: staffId,
          },
        },
      },
    });

    console.log(`   Found ${rewards.length} matching rewards`);

    if (rewards.length === 0) {
      console.log(`   ❌ No active rewards found for event type: ${eventType}`);
      return [];
    }

    const results = [];

    for (const reward of rewards) {
      console.log(`\n   Processing reward ID ${reward.id}:`);
      console.log(`     Description: ${reward.description}`);
      console.log(`     Value: ${reward.value}${reward.valueType === 'PERCENTAGE' ? '%' : ''}`);
      
      // Check if this reward was already given for this entity
      const existingLog = await db.rewardEventLog.findFirst({
        where: {
          staffId,
          rewardId: reward.id,
          entityType,
          entityId: typeof entityId === 'string' ? entityId : entityId?.toString(),
        },
      });

      if (existingLog) {
        console.log(`     ⚠️ Reward already given on ${existingLog.receivedAt}`);
        continue;
      }

      // Calculate actual reward value
      let actualRewardValue = reward.value;
      if (reward.valueType === 'PERCENTAGE') {
        if (!contextAmount) {
          console.log(`     ❌ Percentage reward requires contextAmount but none provided`);
          continue;
        }
        actualRewardValue = (reward.value / 100) * contextAmount;
      }

      console.log(`     Calculated reward: ₦${actualRewardValue}`);

      try {
        // Actually give the reward
        const rewardLog = await giveReward(staffId, reward, entityType, entityId, contextAmount);
        
        if (rewardLog) {
          console.log(`     ✅ Successfully gave reward: ₦${actualRewardValue} to ${staff.fullName}`);
          results.push(rewardLog);
        } else {
          console.log(`     ❌ Failed to give reward`);
        }
      } catch (error) {
        console.log(`     ❌ Error giving reward: ${error.message}`);
      }
    }

    return results;
  } catch (error) {
    console.log(`   ❌ Error in triggerReward: ${error.message}`);
    return [];
  }
}

async function giveReward(staffId, reward, entityType, entityId, contextAmount) {
  try {
    let transactionId;

    // Get staff information
    const staff = await db.staff.findUnique({
      where: { id: staffId },
      select: { locationId: true },
    });

    if (!staff) {
      throw new Error('Staff not found');
    }

    // Calculate actual reward value
    let actualRewardValue = reward.value;
    if (reward.valueType === 'PERCENTAGE') {
      if (!contextAmount) {
        console.log(`Percentage reward requires contextAmount but none provided`);
        return null;
      }
      actualRewardValue = (reward.value / 100) * contextAmount;
    }

    // Update staff balance/points/vouchers based on reward type
    if (reward.valueType === 'CASH' || reward.valueType === 'PERCENTAGE') {
      // Get system account
      const systemAccount = await db.systemAccount.findFirst({
        where: { type: 'REWARDS' },
        include: { account: true }
      });

      if (!systemAccount) {
        throw new Error('System rewards account not found');
      }

      // Create transaction for cash rewards
      const transaction = await db.transaction.create({
        data: {
          fromAccountId: systemAccount.account?.id,
          staffId: staffId,
          amount: actualRewardValue,
          type: 'REWARD',
          status: 'SUCCESS',
          mode: 'internal',
          reference: `REWARD-${Date.now()}`,
          remarks: `Reward: ${reward.eventType}`,
          locationId: staff.locationId,
        },
      });

      transactionId = transaction.id;

      // Update staff wallet
      await db.staff.update({
        where: { id: staffId },
        data: { wallet: { increment: actualRewardValue } },
      });
    } else if (reward.valueType === 'POINTS') {
      // Update staff points
      await db.staff.update({
        where: { id: staffId },
        data: { pointsAccrued: { increment: Math.floor(actualRewardValue) } },
      });
    } else if (reward.valueType === 'VOUCHER') {
      // Update meal voucher
      await db.staff.update({
        where: { id: staffId },
        data: { mealVoucher: { increment: actualRewardValue } },
      });
    }

    // Log the reward event
    const rewardLog = await db.rewardEventLog.create({
      data: {
        staffId,
        rewardId: reward.id,
        value: actualRewardValue,
        valueType: reward.valueType,
        currency: reward.valueType === 'CASH' || reward.valueType === 'PERCENTAGE' ? 'NGN' : undefined,
        eventType: reward.eventType,
        eventData: {
          entityType,
          entityId,
          rewardDescription: reward.description,
          timestamp: new Date(),
        },
        entityType,
        entityId: typeof entityId === 'string' ? entityId : entityId?.toString(),
        transactionId,
      },
    });

    // Create referralCodeUsage for package booking rewards
    if (entityType === 'package_booking' || entityType === 'package_referral') {
      const referralCode = await db.referralCode.findFirst({
        where: { assignedToStaffId: staffId },
      });

      if (referralCode) {
        await db.referralCodeUsage.create({
          data: {
            referralCodeId: referralCode.id,
            dateUsed: new Date(),
            purpose: `${reward.eventType} - ${reward.description || 'Package Referral Reward'}`,
            value: actualRewardValue,
          },
        });
      }
    }

    console.log(`Rewarded staff ${staffId} with ₦${actualRewardValue} ${reward.valueType} for ${reward.eventType}`);
    return rewardLog;
  } catch (error) {
    console.error('Error giving reward:', error);
    throw error;
  }
}

async function manualRewardTrigger() {
  try {
    console.log('🧪 Manually Triggering Rewards for Completed Bookings...\n');

    // Get completed bookings with referral codes that haven't received rewards
    const completedBookings = await db.packageBooking.findMany({
      where: {
        bookingStatus: 'COMPLETED',
        referralCode: { not: null },
        createdAt: {
          gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
        }
      },
      include: {
        packages: {
          select: {
            name: true,
            bonusApplicable: true
          }
        }
      },
      orderBy: { createdAt: 'desc' },
      take: 5
    });

    console.log(`Found ${completedBookings.length} completed bookings to process\n`);

    for (const booking of completedBookings) {
      console.log(`📦 Processing booking: ${booking.id}`);
      console.log(`   Package: ${booking.packages.name}`);
      console.log(`   Referral Code: ${booking.referralCode}`);
      console.log(`   Amount: ₦${booking.totalAmount || booking.amount}`);

      if (!booking.packages.bonusApplicable) {
        console.log(`   ⚠️ Skipping - bonus not applicable`);
        continue;
      }

      // Find the referral code
      const referralCode = await db.referralCode.findUnique({
        where: { code: booking.referralCode },
        include: {
          assignedToStaff: {
            select: {
              id: true,
              fullName: true,
              isActive: true
            }
          }
        }
      });

      if (!referralCode?.isActive || !referralCode.assignedToStaff?.isActive) {
        console.log(`   ⚠️ Skipping - referral code or staff inactive`);
        continue;
      }

      console.log(`   👤 Staff: ${referralCode.assignedToStaff.fullName} (ID: ${referralCode.assignedToStaffId})`);

      // Trigger the onsite reward (since booking is COMPLETED)
      const amount = Number(booking.totalAmount || booking.amount);
      
      await triggerReward(
        'PACKAGE_REFERRAL_ONSITE',
        referralCode.assignedToStaffId,
        'package_booking',
        booking.id,
        amount
      );

      console.log(`\n${'='.repeat(80)}\n`);
    }

    // Check final results
    console.log('📊 Final Results:');
    const rewardLogs = await db.rewardEventLog.findMany({
      where: {
        eventType: {
          in: ['PACKAGE_REFERRAL_ONSITE', 'PACKAGE_REFERRAL_ONLINE']
        },
        receivedAt: {
          gte: new Date(Date.now() - 5 * 60 * 1000) // Last 5 minutes
        }
      },
      include: {
        staff: {
          select: {
            fullName: true
          }
        }
      }
    });

    console.log(`✅ ${rewardLogs.length} rewards given in the last 5 minutes:`);
    rewardLogs.forEach(log => {
      console.log(`   💰 ${log.staff.fullName}: ₦${log.value} for ${log.eventType}`);
    });

  } catch (error) {
    console.error('❌ Error in manual reward trigger:', error);
  } finally {
    await db.$disconnect();
  }
}

manualRewardTrigger();
