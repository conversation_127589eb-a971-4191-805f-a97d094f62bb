const { PrismaClient } = require('@prisma/client');

const db = new PrismaClient();

async function createOnsiteReward() {
  try {
    console.log('🎯 Creating PACKAGE_REFERRAL_ONSITE reward...\n');

    // First check if it already exists
    const existingReward = await db.reward.findFirst({
      where: {
        eventType: 'PACKAGE_REFERRAL_ONSITE',
        isActive: true,
      },
    });

    if (existingReward) {
      console.log('✅ PACKAGE_REFERRAL_ONSITE reward already exists!');
      console.log(`   ID: ${existingReward.id}`);
      console.log(`   Value: ${existingReward.value}% of booking amount`);
      return;
    }

    // Get the same locations as the existing online reward
    const onlineReward = await db.reward.findFirst({
      where: {
        eventType: 'PACKAGE_REFERRAL_ONLINE',
        isActive: true,
      },
      include: {
        location: true,
        eligibleStaff: true,
      },
    });

    if (!onlineReward) {
      throw new Error(
        'No PACKAGE_REFERRAL_ONLINE reward found to copy settings from'
      );
    }

    console.log(
      `Found existing online reward with ${onlineReward.location.length} locations and ${onlineReward.eligibleStaff.length} eligible staff`
    );

    // Create the onsite reward with same settings as online reward
    const reward = await db.reward.create({
      data: {
        description: 'Reward for onsite package bookings with referral codes',
        eventType: 'PACKAGE_REFERRAL_ONSITE',
        valueType: onlineReward.valueType,
        value: onlineReward.value, // Same percentage as online
        isActive: true,
        maxRewardsPerUser: onlineReward.maxRewardsPerUser,
        maxRewardsPerDay: onlineReward.maxRewardsPerDay,
        validFrom: onlineReward.validFrom,
        validUntil: onlineReward.validUntil,
        location: {
          connect: onlineReward.location.map((location) => ({
            id: location.id,
          })),
        },
        eligibleStaff: {
          connect: onlineReward.eligibleStaff.map((staff) => ({
            id: staff.id,
          })),
        },
      },
      include: {
        location: true,
        eligibleStaff: {
          select: {
            id: true,
            fullName: true,
          },
        },
      },
    });

    console.log('\n✅ Successfully created PACKAGE_REFERRAL_ONSITE reward:');
    console.log(`   ID: ${reward.id}`);
    console.log(`   Event Type: ${reward.eventType}`);
    console.log(`   Value: ${reward.value}% of booking amount`);
    console.log(`   Active: ${reward.isActive}`);
    console.log(
      `   Locations: ${reward.location.map((l) => l.name).join(', ')}`
    );
    console.log(`   Eligible Staff: ${reward.eligibleStaff.length}`);

    console.log(
      '\n🎉 Now both online and onsite package referral rewards are configured!'
    );

    // Test the reward system with a recent completed booking
    console.log('\n🧪 Testing reward system...');
    const completedBooking = await db.packageBooking.findFirst({
      where: {
        bookingStatus: 'COMPLETED',
        referralCode: { not: null },
      },
      include: {
        packages: true,
      },
      orderBy: { createdAt: 'desc' },
    });

    if (completedBooking) {
      console.log(`Found completed booking: ${completedBooking.id}`);
      console.log(`Referral code: ${completedBooking.referralCode}`);
      console.log(
        `Bonus applicable: ${completedBooking.packages.bonusApplicable}`
      );

      if (completedBooking.packages.bonusApplicable) {
        const referralCode = await db.referralCode.findUnique({
          where: { code: completedBooking.referralCode },
        });

        if (referralCode?.isActive) {
          console.log(
            `✅ This booking should now trigger rewards for staff ID: ${referralCode.assignedToStaffId}`
          );
        }
      }
    }
  } catch (error) {
    console.error('❌ Error creating onsite reward:', error);
    if (error.code === 'P2002') {
      console.log(
        'This might be a unique constraint violation. The reward might already exist.'
      );
    }
  } finally {
    await db.$disconnect();
  }
}

createOnsiteReward();
