import { Request, Response } from 'express';
import { controllerOperations } from '../handlers/handleController';
import { SuggestionBoxService } from '../../services/suggestion';

const CreateSuggestionHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    SuggestionBoxService.createSuggestion,
    req.body,
    res,
    staffId
  );
};

const ListSuggestionsHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    SuggestionBoxService.getAllSuggestions,
    req.query,
    res,
    staffId
  );
};

const UpdateSuggestionHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    SuggestionBoxService.updateSuggestion,
    req.body,
    res,
    staffId
  );
};

const DeleteSuggestionHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  const { suggestionId } = req.params;
  controllerOperations(
    SuggestionBoxService.deleteSuggestion,
    parseInt(suggestionId),
    res,
    staffId
  );
};

export const suggestionControllers = {
  CreateSuggestionHandler,
  ListSuggestionsHandler,
  UpdateSuggestionHandler,
  DeleteSuggestionHandler,
};
