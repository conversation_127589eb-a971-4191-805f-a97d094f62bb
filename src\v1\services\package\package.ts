import { db } from '../../utils/model';
import { extractPublicId } from 'cloudinary-build-url';
import { HttpError } from '../../utils/httpError';
import { formatString } from '../../utils/stringFormatter';
import { staffHasPermission, PERMISSIONS } from '../../utils/permission';
import { logger, devLog } from '../../utils/logger';

const cloudinary = require('../../utils/image/imageStorage'); // eslint-disable-line

// Utility function to shuffle array
const shuffleArray = <T>(array: T[]): T[] => {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
};

export const packageService = {
  getAllPackages: async (staffId: any, query: any) => {
    try {
      const page: number = parseInt(query.page as string);
      const limit: number = parseInt(query.limit as string);
      const category = query.category as string;
      const status = query.status as string | undefined;
      const currentDate = new Date();

      // Fetch all packages without pagination
      const [allPackages, totalCount] = await db.$transaction([
        db.package.findMany({
          where: {
            totalSlot: {
              gt: 0,
            },
            category: {
              slug: category,
            },
            packageStatus: true,
          },
          include: {
            category: {
              select: {
                name: true,
                slug: true,
              },
            },
            packageLocationPrices: {
              select: {
                endDate: true,
                location: {
                  select: {
                    name: true,
                  },
                },
                id: true,
                currency: true,
                amount: true,
                modifiers: {
                  select: {
                    modifierCode: true,
                    amount: true,
                    percentage: true,
                    startDate: true,
                    endDate: true,
                    isActive: true,
                  },
                },
              },
            },
          },
        }),
        db.package.count(),
      ]);

      const validPackages = allPackages
        .map((pkg) => {
          const validLocationPrices = pkg.packageLocationPrices.filter(
            (price) => {
              if (!price.endDate) return true;

              const endDate = new Date(price.endDate);
              endDate.setHours(0, 0, 0, 0);
              const today = new Date(currentDate);
              today.setHours(0, 0, 0, 0);

              return endDate >= today;
            }
          );
          return {
            ...pkg,
            packageLocationPrices: validLocationPrices,
          };
        })
        .filter((pkg) => pkg.packageLocationPrices.length > 0);

      // // Shuffle all valid packages, then apply pagination
      // const shuffledPackages = shuffleArray(validPackages);
      // const startIndex = (page - 1) * limit;
      // const paginatedPackages = shuffledPackages.slice(
      //   startIndex,
      //   startIndex + limit
      // );

      return {
        packages: validPackages,
        totalPages: Math.ceil(validPackages.length / limit),
        totalCount: totalCount,
      };
    } catch (error) {
      logger.error('Error getting all packages:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to fetch packages', 400);
    }
  },

  getAllPackagesAdmin: async (staffId: any, query: any) => {
    try {
      await staffHasPermission(staffId, PERMISSIONS.PACKAGE_VIEW);
      const page: number = parseInt(query.page as string);
      const limit: number = parseInt(query.limit as string);

      const [packages, totalPages] = await db.$transaction([
        db.package.findMany({
          orderBy: { createdAt: 'desc' },
          take: limit,
          skip: (page - 1) * limit,
          include: {
            category: true,
            packageLocationPrices: {
              include: {
                location: true,
                modifiers: true,
              },
            },
            tests: true,
            _count: {
              select: {
                packageBookings: {
                  where: {
                    bookingStatus: 'COMPLETED',
                  },
                },
              },
            },
          },
        }),
        db.package.count(),
      ]);

      const packageWithBookingCount = packages.map((pkg) => ({
        ...pkg,
        packageBookings: pkg._count.packageBookings,
      }));

      return {
        packages: packageWithBookingCount,
        totalPages: Math.ceil(totalPages / limit),
      };
    } catch (error) {
      logger.error('Error getting all packages:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to fetch packages', 400);
    }
  },

  getAllPackageForLink: async () => {
    try {
      const currentDate = new Date();
      const packages = await db.package.findMany({
        where: {
          packageStatus: true,
          totalSlot: {
            gt: 0,
          },
        },
        select: {
          id: true,
          slug: true,
          name: true,
          packageLocationPrices: {
            select: {
              endDate: true,
            },
          },
        },
      });

      const validPackages = packages.filter((pkg) => {
        const hasValidPrices = pkg.packageLocationPrices.some((price) => {
          if (!price.endDate) return true;
          const endDate = new Date(price.endDate);
          endDate.setHours(0, 0, 0, 0);
          const today = new Date(currentDate);
          today.setHours(0, 0, 0, 0);
          return endDate >= today;
        });

        return hasValidPrices;
      });

      return validPackages.map((pkg) => ({
        id: pkg.id,
        slug: pkg.slug,
        name: pkg.name,
      }));
    } catch (error) {
      logger.error('Error getting all packages for link:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to fetch packages for link', 400);
    }
  },

  getSinglePackageAdmin: async (staffId: any, slug: string) => {
    try {
      await staffHasPermission(staffId, PERMISSIONS.PACKAGE_VIEW);
      const packageExist = await db.package.findUnique({
        where: { slug: slug },
      });
      if (!packageExist) {
        throw new HttpError('Package cannot be found', 404);
      }

      return db.package.findUnique({
        where: { slug: slug },
        include: {
          category: true,
          packageLocationPrices: {
            include: {
              location: true,
              modifiers: true,
            },
          },
          tests: true,
        },
      });
    } catch (error) {
      logger.error('Error getting single package:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to fetch package', 400);
    }
  },

  getSinglePackage: async (slug: string) => {
    try {
      const packageExist = await db.package.findUnique({
        where: { slug: slug },
      });
      if (!packageExist) {
        throw new HttpError('Package cannot be found', 404);
      }

      const currentDate = new Date();
      const packageData = await db.package.findUnique({
        where: { slug: slug },
        include: {
          category: true,
          packageLocationPrices: {
            include: {
              location: {
                include: {
                  region: {
                    select: {
                      name: true,
                    },
                  },
                },
              },
              modifiers: true,
            },
          },
          tests: true,
        },
      });

      if (!packageData) {
        throw new HttpError('Package cannot be found', 404);
      }

      const validLocationPrices = packageData.packageLocationPrices.filter(
        (price) => {
          if (!price.endDate) return true;

          const endDate = new Date(price.endDate);
          endDate.setHours(0, 0, 0, 0);
          const today = new Date(currentDate);
          today.setHours(0, 0, 0, 0);

          return endDate >= today;
        }
      );

      if (
        validLocationPrices.length === 0 &&
        packageData.packageLocationPrices.length > 0
      ) {
        throw new HttpError(
          'This package is no longer available as all pricing options have expired',
          400
        );
      }

      return {
        ...packageData,
        packageLocationPrices: validLocationPrices,
      };
    } catch (error) {
      logger.error('Error getting single package:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to fetch package', 400);
    }
  },

  createPackage: async (staffId: any, filePath: any, reqBody: any) => {
    try {
      const { name, totalSlot, categoryId, prices, testIds, ...rest } = reqBody;
      await staffHasPermission(staffId, PERMISSIONS.PACKAGE_CREATE);

      const formattedName = formatString.trimString(name);
      const checkPackage = await db.package.findFirst({
        where: { name: formattedName },
      });
      if (checkPackage) {
        throw new HttpError('Package with the title exists already', 400);
      }
      const slug = formatString.formatSlug(name);
      const result = await cloudinary.uploader.upload(filePath, {
        public_id: `${slug}`,
        folder: 'packages',
        fetch_format: 'auto',
        transformation: [{ quality: 'auto', format: 'auto' }],
      });

      const testIdsArray = Array.isArray(testIds)
        ? testIds
        : JSON.parse(testIds);
      const pricesArray = Array.isArray(prices) ? prices : JSON.parse(prices);

      const newPackage = await db.package.create({
        data: {
          ...rest,
          name: formattedName,
          slug: slug,
          totalSlot: Number(totalSlot),
          categoryId: Number(categoryId),
          packageImage: result.secure_url,
          tests: {
            connect: testIdsArray.map((testId: string) => ({
              id: Number(testId),
            })),
          },
        },
      });

      if (prices && Array.isArray(pricesArray)) {
        for (const price of pricesArray) {
          const formatDate = new Date(price.endDate);
          await db.packageLocationPrice.create({
            data: {
              amount: Number(price.amount),
              currency: price.currency,
              locationId: Number(price.location.id),
              packageId: newPackage.id,
              endDate: formatDate.toISOString(),
            },
          });
        }
      }
      return newPackage;
    } catch (error) {
      logger.error('Error creating package:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to create package', 400);
    }
  },

  updatePackage: async (staffId: any, reqBody: any) => {
    try {
      await staffHasPermission(staffId, PERMISSIONS.PACKAGE_EDIT);

      const packageId = Number(reqBody.id);
      const categoryId = Number(reqBody.categoryId);

      if (isNaN(packageId)) {
        throw new HttpError('Invalid package ID', 400);
      }

      const existingPackage = await db.package.findUnique({
        where: { id: packageId },
        include: { tests: true },
      });

      if (!existingPackage) {
        throw new HttpError('Package does not exist', 400);
      }

      const updateData: any = {};
      let hasChanges = false;

      // Validate and update name
      if (reqBody.name) {
        const formattedName = formatString.trimString(reqBody.name);
        if (formattedName !== existingPackage.name) {
          const nameExists = await db.package.findFirst({
            where: { name: formattedName, id: { not: packageId } },
          });

          if (nameExists) {
            throw new HttpError('Package name already exists', 400);
          }

          updateData.name = formattedName;
          hasChanges = true;
        }
      }

      // Check and update optional fields
      const fieldsToCheck = [
        'totalSlot',
        'basePrice',
        'description',
        'bonusApplicable',
        'includeHospitalReg',
      ] as const;

      for (const field of fieldsToCheck) {
        const newValue = reqBody[field];
        const oldValue = existingPackage[field];
        if (newValue !== undefined && newValue !== oldValue) {
          updateData[field] = newValue;
          hasChanges = true;
        }
      }

      if (
        categoryId !== undefined &&
        categoryId !== existingPackage.categoryId
      ) {
        updateData.categoryId = categoryId;
        hasChanges = true;
      }

      // Handle testIds relationship
      if (Array.isArray(reqBody.testIds)) {
        const newTestIds = Array.from(
          new Set<number>(reqBody.testIds.map((id: any) => Number(id)))
        ).sort((a, b) => a - b);

        const existingTestIds = existingPackage.tests
          .map((t) => Number(t.id))
          .sort((a, b) => a - b);

        const testsChanged =
          newTestIds.length !== existingTestIds.length ||
          newTestIds.some((id, i) => id !== existingTestIds[i]);

        if (testsChanged) {
          updateData.tests = {
            set: newTestIds.map((id) => ({ id })),
          };
          hasChanges = true;
        }
      }

      if (!hasChanges) {
        return existingPackage; // no changes to apply
      }

      return db.package.update({
        where: { id: packageId },
        data: updateData,
        include: { tests: true },
      });
    } catch (error) {
      logger.error('Error updating package:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to update package', 400);
    }
  },

  updatePackageImage: async (staffId: any, filePath: any, reqBody: any) => {
    try {
      await staffHasPermission(staffId, PERMISSIONS.PACKAGE_EDIT);
      const { packageId } = reqBody;
      if (!packageId) {
        throw new HttpError('Package ID is required', 400);
      }
      const existingPackage = await db.package.findUnique({
        where: { id: Number(packageId) },
      });
      if (!existingPackage) {
        throw new HttpError('Package not found', 404);
      }

      const oldImage = extractPublicId(existingPackage.packageImage);
      await cloudinary.uploader.destroy(oldImage, {
        invalidate: true,
        resource_type: 'image',
      });
      const result = await cloudinary.uploader.upload(filePath, {
        public_id: `${existingPackage.slug}`,
        folder: 'packages',
        fetch_format: 'auto',
        transformation: [{ quality: 'auto', format: 'auto' }],
      });
      return db.package.update({
        where: { id: Number(packageId) },
        data: {
          packageImage: result.secure_url,
        },
      });
    } catch (error) {
      logger.error('Error updating package image:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to update package image', 400);
    }
  },

  updatePackageStatus: async (staffId: any, reqBody: any) => {
    try {
      await staffHasPermission(staffId, PERMISSIONS.PACKAGE_EDIT);
      const { id, packageStatus } = reqBody;

      const packageExist = await db.package.findUnique({
        where: { id: Number(id) },
      });
      if (!packageExist) {
        throw new HttpError('Package cannot be found', 400);
      }
      return db.package.update({
        where: { id: Number(id) },
        data: {
          packageStatus: packageStatus,
        },
      });
    } catch (error) {
      logger.error('Error updating package status:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to update package status', 400);
    }
  },

  // deletePackage: async (staffId: string, id: string) => {
  //   await adminHasPermission(staffId, PERMISSIONS.PACKAGE_DELETE);
  //   const packageExist = await db.package.delete({
  //     where: { id: Number(id) },
  //   });
  //   if (!packageExist) {
  //     throw new HttpError('Package not found', 404);
  //   }
  //   return { message: 'Package removed successfully' };
  // },
};
