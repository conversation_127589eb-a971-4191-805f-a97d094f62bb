import { Request, Response } from 'express';
import { controllerOperations } from '../handlers/handleController';
import { innovationIdeaService } from '../../services/innovation/innovationIdea';
import { innovationIdeaLikeService } from '../../services/innovation/innovationIdeaLike';
import { innovationIdeaCommentService } from '../../services/innovation/innovationIdeaComment';

// Innovation Idea Controllers
const CreateInnovationIdeaHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    innovationIdeaService.createInnovationIdea,
    req.body,
    res,
    staffId
  );
};

const GetAllCategoriesHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    innovationIdeaService.getAllCategories,
    undefined,
    res,
    staffId
  );
};

const GetAllTagsHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    innovationIdeaService.getAllTags,
    undefined,
    res,
    staffId
  );
};

const GetInnovationIdeasStats = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    innovationIdeaService.getInnovationIdeasStats,
    undefined,
    res,
    staffId
  );
};

const GetAllInnovationIdeasHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    innovationIdeaService.getAllInnovationIdeas,
    req.query,
    res,
    staffId
  );
};

const UpdateInnovationIdeaHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    innovationIdeaService.updateInnovationIdea,
    req.body,
    res,
    staffId
  );
};

const DeleteInnovationIdeaHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  const { ideaId } = req.params;
  controllerOperations(
    innovationIdeaService.deleteInnovationIdea,
    parseInt(ideaId),
    res,
    staffId
  );
};

// Innovation Idea Like Controllers
const ToggleLikeHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  const { ideaId } = req.params;
  controllerOperations(
    innovationIdeaLikeService.toggleLike,
    parseInt(ideaId),
    res,
    staffId
  );
};

const CreateCommentHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    innovationIdeaCommentService.createComment,
    req.body,
    res,
    staffId
  );
};

const DeleteCommentHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  const { commentId } = req.params;
  controllerOperations(
    innovationIdeaCommentService.deleteComment,
    parseInt(commentId),
    res,
    staffId
  );
};

const GetInnovationLeaderboardHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    innovationIdeaService.getInnovationLeaderboard,
    req.query,
    res,
    staffId
  );
};

export const innovationIdeaControllers = {
  CreateInnovationIdeaHandler,
  GetAllInnovationIdeasHandler,
  UpdateInnovationIdeaHandler,
  DeleteInnovationIdeaHandler,
  ToggleLikeHandler,
  CreateCommentHandler,
  DeleteCommentHandler,
  GetAllCategoriesHandler,
  GetAllTagsHandler,
  GetInnovationIdeasStats,
  GetInnovationLeaderboardHandler,
};
