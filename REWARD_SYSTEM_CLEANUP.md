# Reward System Architecture Cleanup

## Overview

Successfully cleaned up the reward system architecture by removing the legacy implementation and consolidating to use only the dynamic reward system. This eliminates confusion and technical debt while providing a cleaner, more maintainable codebase.

## Changes Made

### 1. Service Layer Cleanup (`src/v1/services/reward/reward.ts`)

**Removed Legacy Methods:**

- `getAllReward()` - Basic reward listing with limited filtering
- `createReward()` - Simple reward creation without advanced features
- `updateReward()` - Basic reward updates
- `deleteReward()` - Simple reward deletion

**Consolidated to Dynamic Methods:**

- `getAllRewards()` - Advanced filtering, pagination, includes related data
- `createReward()` - Full-featured reward creation with validation
- `updateReward()` - Comprehensive update with conflict checking
- `deleteReward()` - Safe deletion with proper validation
- `toggleRewardStatus()` - Status management for rewards and rules

**Kept Essential Methods:**

- `verifyReferralCode()` - Validates referral codes for reward eligibility
- `getAllRewardRules()` - Manages reward rules and conditions
- `getRewardStatistics()` - Provides reward analytics and statistics
- `getStaffRewardHistory()` - Retrieves individual staff reward history

### 2. Controller Layer Updates (`src/v1/controllers/reward/reward.controller.ts`)

**Removed Duplicate Handlers:**

- Eliminated "Dynamic" prefixed handlers that were redundant
- Consolidated to single set of handlers for each operation

**Updated Handler Logic:**

- Fixed parameter passing for `updateReward` to match new signature
- Updated method calls to use consolidated service methods
- Improved parameter handling for automatic calculation handlers

### 3. Route Consolidation (`src/v1/routes/router/reward.route.ts`)

**Before (Confusing):**

```
GET    /reward/list                    # Legacy
GET    /reward/dynamic/list           # New
POST   /reward/create                 # Legacy
POST   /reward/dynamic/create         # New
PATCH  /reward/update                 # Legacy
PATCH  /reward/dynamic/update         # New
```

**After (Clean):**

```
GET    /reward/list                   # Unified
POST   /reward/create                 # Unified
PATCH  /reward/update                 # Unified
PATCH  /reward/toggle-status          # Status management
DELETE /reward/:rewardId              # Deletion
```

### 4. Import Cleanup

- Removed unused imports from service and controller files
- Cleaned up dependencies that were no longer needed

## Benefits Achieved

### ✅ **Eliminated Confusion**

- Single, consistent API for reward management
- No more duplicate endpoints with different behaviors
- Clear naming conventions without "dynamic" prefixes

### ✅ **Improved Maintainability**

- Reduced code duplication
- Single source of truth for reward operations
- Easier to add new features and fix bugs

### ✅ **Better Developer Experience**

- Frontend developers only need to learn one set of endpoints
- Consistent request/response patterns
- Better error handling and validation

### ✅ **Enhanced Functionality**

- All endpoints now use the advanced dynamic system features
- Better filtering, pagination, and data relationships
- Comprehensive validation and error handling

# Frontend Developer Guide

## Complete API Reference

### 🎯 Core Reward Management

#### 1. List All Rewards

```http
GET /api/v1/reward/list
Authorization: Bearer <token>
```

**Query Parameters:**

```javascript
{
  isActive?: 'true' | 'false',     // Filter by active status
  eventType?: string,              // Filter by event type (INNOVATION_IDEA_POSTED, PACKAGE_CONFIRMED, etc.)
  valueType?: string,              // Filter by value type (CASH, POINTS, PERCENTAGE, VOUCHER)
  purpose?: string,                // Filter by purpose (REFERRAL, MILESTONE, ACHIEVEMENT)
  locationId?: number,             // Filter by location (includes global rewards)
  page?: number,                   // Page number (default: 1)
  limit?: number                   // Items per page (default: 50)
}
```

**Response:**

```javascript
{
  "success": true,
  "data": {
    "rewards": [
      {
        "id": 1,
        "name": "Innovation Idea Reward",
        "description": "Reward for posting innovation ideas",
        "eventType": "INNOVATION_IDEA_POSTED",
        "valueType": "CASH",
        "value": 1000,
        "purpose": "INNOVATION",
        "isActive": true,
        "locationId": null,
        "conditions": {},
        "maxRewardsPerUser": 10,
        "maxRewardsPerDay": 100,
        "validFrom": "2024-01-01T00:00:00.000Z",
        "validUntil": null,
        "createdAt": "2024-01-01T00:00:00.000Z",
        "location": null,
        "rules": [
          {
            "id": 1,
            "name": "Basic Innovation Rule",
            "priority": 10,
            "isActive": true
          }
        ],
        "_count": {
          "staffRewards": 45,
          "rules": 2
        }
      }
    ],
    "pagination": {
      "total": 25,
      "page": 1,
      "limit": 50,
      "totalPages": 1
    }
  }
}
```

#### 2. Create New Reward

```http
POST /api/v1/reward/create
Authorization: Bearer <token>
Content-Type: application/json
```

**Request Body:**

```javascript
{
  "name": "Package Booking Reward",
  "description": "Reward for confirmed package bookings",
  "eventType": "PACKAGE_CONFIRMED",        // Required: Event that triggers reward
  "valueType": "CASH",                     // Required: CASH, POINTS, PERCENTAGE, VOUCHER
  "value": 500,                           // Required: Reward value
  "purpose": "BOOKING",                   // Optional: REFERRAL, MILESTONE, ACHIEVEMENT, etc.
  "locationId": 1,                        // Optional: null for global rewards
  "conditions": {                         // Optional: Complex conditions
    "minBookingAmount": 10000,
    "requiredTags": ["premium"]
  },
  "maxRewardsPerUser": 5,                 // Optional: Max per user
  "maxRewardsPerDay": 50,                 // Optional: Max per day globally
  "maxRewardsPerUserPerDay": 1,           // Optional: Max per user per day
  "validFrom": "2024-01-01T00:00:00.000Z", // Optional: Start date
  "validUntil": "2024-12-31T23:59:59.999Z" // Optional: End date
}
```

**Response:**

```javascript
{
  "success": true,
  "data": {
    "id": 2,
    "name": "Package Booking Reward",
    "description": "Reward for confirmed package bookings",
    "eventType": "PACKAGE_CONFIRMED",
    "valueType": "CASH",
    "value": 500,
    "isActive": true,
    // ... other fields
  }
}
```

#### 3. Update Existing Reward ⚠️ BREAKING CHANGE

```http
PATCH /api/v1/reward/update
Authorization: Bearer <token>
Content-Type: application/json
```

**Request Body (NEW FORMAT):**

```javascript
{
  "rewardId": 2,                          // Required: ID of reward to update
  "name": "Updated Package Reward",       // Optional: New name
  "description": "Updated description",   // Optional: New description
  "value": 750,                          // Optional: New value
  "conditions": {                        // Optional: Updated conditions
    "minBookingAmount": 15000
  },
  "maxRewardsPerUser": 10               // Optional: Updated limits
}
```

**Response:**

```javascript
{
  "success": true,
  "data": {
    "id": 2,
    "name": "Updated Package Reward",
    "value": 750,
    // ... updated fields
  }
}
```

#### 4. Toggle Reward Status

```http
PATCH /api/v1/reward/toggle-status
Authorization: Bearer <token>
Content-Type: application/json
```

**Request Body:**

```javascript
{
  "rewardId": 2,
  "isActive": false    // true to activate, false to deactivate
}
```

**Response:**

```javascript
{
  "success": true,
  "data": {
    "message": "Reward deactivated successfully",
    "reward": {
      "id": 2,
      "name": "Package Booking Reward",
      "isActive": false,
      // ... other fields
    }
  }
}
```

#### 5. Delete Reward

```http
DELETE /api/v1/reward/:rewardId
Authorization: Bearer <token>
```

**Response:**

```javascript
{
  "success": true,
  "data": {
    "message": "Reward deleted successfully"
  }
}
```

### 🔧 Reward Rules Management

#### 6. Create Reward Rule

```http
POST /api/v1/reward/rules/create
Authorization: Bearer <token>
Content-Type: application/json
```

**Request Body:**

```javascript
{
  "name": "Premium Package Rule",
  "description": "Rule for premium package bookings",
  "eventType": "PACKAGE_CONFIRMED",
  "rewardId": 2,                          // Required: ID of reward to trigger
  "conditions": {                         // Optional: Rule-specific conditions
    "packageType": "premium",
    "minAmount": 20000
  },
  "priority": 10,                         // Optional: Higher = evaluated first
  "maxUsagePerUser": 3,                   // Optional: Max times per user
  "maxUsagePerDay": 20,                   // Optional: Max times per day
  "maxUsageTotal": 1000,                  // Optional: Max total usage
  "validFrom": "2024-01-01T00:00:00.000Z", // Optional: Rule start date
  "validUntil": "2024-12-31T23:59:59.999Z", // Optional: Rule end date
  "locationId": 1                         // Optional: Location restriction
}
```

#### 7. List Reward Rules

```http
GET /api/v1/reward/rules/list
Authorization: Bearer <token>
```

**Query Parameters:**

```javascript
{
  isActive?: 'true' | 'false',
  eventType?: string,
  rewardId?: number,
  locationId?: number,
  page?: number,
  limit?: number
}
```

#### 8. Update Reward Rule

```http
PATCH /api/v1/reward/rules/update
Authorization: Bearer <token>
Content-Type: application/json
```

**Request Body:**

```javascript
{
  "ruleId": 1,
  "priority": 15,
  "maxUsagePerUser": 5,
  "conditions": {
    "minAmount": 25000
  }
}
```

#### 9. Toggle Rule Status

```http
PATCH /api/v1/reward/rules/toggle-status
Authorization: Bearer <token>
Content-Type: application/json
```

**Request Body:**

```javascript
{
  "ruleId": 1,
  "isActive": false
}
```

#### 10. Delete Reward Rule

```http
DELETE /api/v1/reward/rules/:ruleId
Authorization: Bearer <token>
```

### 📊 Statistics & Analytics

#### 11. Get Reward Statistics

```http
GET /api/v1/reward/statistics
Authorization: Bearer <token>
```

**Query Parameters:**

```javascript
{
  startDate?: 'YYYY-MM-DD',
  endDate?: 'YYYY-MM-DD',
  locationId?: number
}
```

**Response:**

```javascript
{
  "success": true,
  "data": {
    "totalRewards": 25,
    "activeRewards": 20,
    "totalRules": 45,
    "activeRules": 40,
    "totalDistributed": 1250,
    "recentDistributions": [
      {
        "id": 1,
        "staffId": 123,
        "rewardId": 2,
        "value": 500,
        "receivedAt": "2024-01-15T10:30:00.000Z",
        "reward": {
          "name": "Package Booking Reward",
          "valueType": "CASH"
        },
        "staff": {
          "fullName": "John Doe",
          "email": "<EMAIL>"
        }
      }
    ]
  }
}
```

#### 12. Get Staff Reward History

```http
GET /api/v1/reward/staff/:targetStaffId/history
Authorization: Bearer <token>
```

**Query Parameters:**

```javascript
{
  startDate?: 'YYYY-MM-DD',
  endDate?: 'YYYY-MM-DD',
  eventType?: string,
  page?: number,
  limit?: number
}
```

### 📈 Advanced Analytics

#### 13. Get Reward Dashboard

```http
GET /api/v1/reward/analytics/dashboard
Authorization: Bearer <token>
```

**Query Parameters:**

```javascript
{
  startDate?: 'YYYY-MM-DD',
  endDate?: 'YYYY-MM-DD',
  locationId?: number,
  eventType?: string,
  valueType?: string
}
```

**Response:**

```javascript
{
  "success": true,
  "data": {
    "summary": {
      "totalRewards": 25,
      "totalDistributed": 1250,
      "totalValue": 125000,
      "activeRewards": 20,
      "activeRules": 40
    },
    "distribution": {
      "byEventType": [
        { "eventType": "INNOVATION_IDEA_POSTED", "count": 450, "value": 45000 },
        { "eventType": "PACKAGE_CONFIRMED", "count": 300, "value": 60000 }
      ],
      "byValueType": [
        { "valueType": "CASH", "count": 600, "value": 80000 },
        { "valueType": "POINTS", "count": 150, "value": 25000 }
      ],
      "topStaff": [
        {
          "staffId": 123,
          "fullName": "John Doe",
          "email": "<EMAIL>",
          "totalRewards": 15,
          "totalValue": 7500,
          "location": "Main Branch"
        }
      ]
    },
    "trends": {
      "monthlyData": [
        { "month": "2024-01", "count": 120, "value": 15000 },
        { "month": "2024-02", "count": 135, "value": 18000 }
      ]
    }
  }
}
```

#### 14. Get Staff Leaderboard

```http
GET /api/v1/reward/analytics/leaderboard
Authorization: Bearer <token>
```

**Query Parameters:**

```javascript
{
  startDate?: 'YYYY-MM-DD',
  endDate?: 'YYYY-MM-DD',
  locationId?: number,
  limit?: number    // Default: 50
}
```

#### 15. Export Reward Data

```http
GET /api/v1/reward/analytics/export
Authorization: Bearer <token>
```

**Query Parameters:**

```javascript
{
  startDate?: 'YYYY-MM-DD',
  endDate?: 'YYYY-MM-DD',
  locationId?: number,
  format?: 'csv' | 'excel'    // Default: csv
}
```

**Response:** File download (CSV/Excel format)

### 🔍 Referral Code Validation

#### 16. Validate Referral Code

```http
POST /api/v1/reward/referral/validate
Content-Type: application/json
```

**Request Body:**

```javascript
{
  "code": "REF123ABC"
}
```

**Response:**

```javascript
{
  "success": true,
  "data": {
    "message": "Code still active"
  }
}
```

### ⚙️ Administrative Operations

#### 17. Process Milestone Rewards

```http
POST /api/v1/reward/automatic/milestones
Authorization: Bearer <token>
```

#### 18. Process Achievement Rewards

```http
POST /api/v1/reward/automatic/achievements
Authorization: Bearer <token>
```

#### 19. Run All Automatic Calculations

```http
POST /api/v1/reward/automatic/run-all
Authorization: Bearer <token>
```

## Migration Guide for Frontend Developers

### ⚠️ Breaking Changes

#### 1. Update Reward Endpoint

**OLD:**

```javascript
// Update reward
const response = await fetch('/api/v1/reward/update', {
  method: 'PATCH',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    id: 123, // OLD: used 'id'
    name: 'New Name',
    value: 500,
  }),
});
```

**NEW:**

```javascript
// Update reward
const response = await fetch('/api/v1/reward/update', {
  method: 'PATCH',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    rewardId: 123, // NEW: use 'rewardId'
    name: 'New Name',
    value: 500,
  }),
});
```

#### 2. Remove Dynamic Endpoints

**Remove all references to:**

- `/api/v1/reward/dynamic/*` endpoints
- Any "Dynamic" prefixed function calls
- Duplicate reward management code

#### 3. Use New Status Management

**OLD:**

```javascript
// Manual status update
await fetch('/api/v1/reward/update', {
  method: 'PATCH',
  body: JSON.stringify({ id: 123, isActive: false }),
});
```

**NEW:**

```javascript
// Dedicated status toggle
await fetch('/api/v1/reward/toggle-status', {
  method: 'PATCH',
  body: JSON.stringify({ rewardId: 123, isActive: false }),
});
```

### ✅ Enhanced Features to Leverage

#### 1. Advanced Filtering

```javascript
// Get rewards with advanced filtering
const rewards = await fetch(
  '/api/v1/reward/list?' +
    new URLSearchParams({
      isActive: 'true',
      eventType: 'INNOVATION_IDEA_POSTED',
      locationId: '1',
      page: '1',
      limit: '20',
    })
);
```

#### 2. Comprehensive Analytics

```javascript
// Get detailed analytics dashboard
const analytics = await fetch(
  '/api/v1/reward/analytics/dashboard?' +
    new URLSearchParams({
      startDate: '2024-01-01',
      endDate: '2024-12-31',
      locationId: '1',
    })
);
```

#### 3. Export Functionality

```javascript
// Export reward data
const exportUrl =
  '/api/v1/reward/analytics/export?' +
  new URLSearchParams({
    startDate: '2024-01-01',
    endDate: '2024-12-31',
    format: 'excel',
  });

// Trigger download
window.open(exportUrl);
```

## Error Handling

### Common Error Responses

```javascript
// Validation Error (400)
{
  "success": false,
  "error": "Active reward with this name already exists",
  "statusCode": 400
}

// Not Found (404)
{
  "success": false,
  "error": "Reward not found",
  "statusCode": 404
}

// Permission Error (403)
{
  "success": false,
  "error": "Insufficient permissions",
  "statusCode": 403
}

// Server Error (500)
{
  "success": false,
  "error": "Failed to create reward",
  "statusCode": 500
}
```

### Recommended Error Handling

```javascript
async function createReward(rewardData) {
  try {
    const response = await fetch('/api/v1/reward/create', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(rewardData),
    });

    const result = await response.json();

    if (!result.success) {
      throw new Error(result.error || 'Unknown error occurred');
    }

    return result.data;
  } catch (error) {
    console.error('Failed to create reward:', error);
    throw error;
  }
}
```

## TypeScript/JavaScript Helper Functions

### Reward Management Service

```typescript
interface RewardData {
  name: string;
  description: string;
  eventType: string;
  valueType: 'CASH' | 'POINTS' | 'PERCENTAGE' | 'VOUCHER';
  value: number;
  purpose?: string;
  locationId?: number;
  conditions?: Record<string, any>;
  maxRewardsPerUser?: number;
  maxRewardsPerDay?: number;
  maxRewardsPerUserPerDay?: number;
  validFrom?: string;
  validUntil?: string;
}

interface RewardUpdateData {
  rewardId: number;
  name?: string;
  description?: string;
  value?: number;
  conditions?: Record<string, any>;
  maxRewardsPerUser?: number;
  maxRewardsPerDay?: number;
  maxRewardsPerUserPerDay?: number;
  validFrom?: string;
  validUntil?: string;
}

interface RewardListQuery {
  isActive?: 'true' | 'false';
  eventType?: string;
  valueType?: string;
  purpose?: string;
  locationId?: number;
  page?: number;
  limit?: number;
}

class RewardService {
  private baseUrl = '/api/v1/reward';
  private token: string;

  constructor(token: string) {
    this.token = token;
  }

  private async request(endpoint: string, options: RequestInit = {}) {
    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${this.token}`,
        ...options.headers,
      },
    });

    const result = await response.json();

    if (!result.success) {
      throw new Error(result.error || 'Request failed');
    }

    return result.data;
  }

  async listRewards(query: RewardListQuery = {}) {
    const params = new URLSearchParams();
    Object.entries(query).forEach(([key, value]) => {
      if (value !== undefined) params.append(key, String(value));
    });

    return this.request(`/list?${params}`);
  }

  async createReward(data: RewardData) {
    return this.request('/create', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async updateReward(data: RewardUpdateData) {
    return this.request('/update', {
      method: 'PATCH',
      body: JSON.stringify(data),
    });
  }

  async toggleRewardStatus(rewardId: number, isActive: boolean) {
    return this.request('/toggle-status', {
      method: 'PATCH',
      body: JSON.stringify({ rewardId, isActive }),
    });
  }

  async deleteReward(rewardId: number) {
    return this.request(`/${rewardId}`, {
      method: 'DELETE',
    });
  }

  async getStatistics(
    query: { startDate?: string; endDate?: string; locationId?: number } = {}
  ) {
    const params = new URLSearchParams();
    Object.entries(query).forEach(([key, value]) => {
      if (value !== undefined) params.append(key, String(value));
    });

    return this.request(`/statistics?${params}`);
  }

  async getStaffHistory(
    staffId: number,
    query: {
      startDate?: string;
      endDate?: string;
      page?: number;
      limit?: number;
    } = {}
  ) {
    const params = new URLSearchParams();
    Object.entries(query).forEach(([key, value]) => {
      if (value !== undefined) params.append(key, String(value));
    });

    return this.request(`/staff/${staffId}/history?${params}`);
  }

  async validateReferralCode(code: string) {
    return this.request('/referral/validate', {
      method: 'POST',
      body: JSON.stringify({ code }),
    });
  }
}
```

### React Hook Example

```typescript
import { useState, useEffect } from 'react';

interface UseRewardsOptions {
  autoFetch?: boolean;
  query?: RewardListQuery;
}

export function useRewards(token: string, options: UseRewardsOptions = {}) {
  const [rewards, setRewards] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const rewardService = new RewardService(token);

  const fetchRewards = async (query: RewardListQuery = {}) => {
    setLoading(true);
    setError(null);

    try {
      const data = await rewardService.listRewards(query);
      setRewards(data.rewards);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch rewards');
    } finally {
      setLoading(false);
    }
  };

  const createReward = async (data: RewardData) => {
    try {
      const newReward = await rewardService.createReward(data);
      setRewards((prev) => [...prev, newReward]);
      return newReward;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create reward');
      throw err;
    }
  };

  const updateReward = async (data: RewardUpdateData) => {
    try {
      const updatedReward = await rewardService.updateReward(data);
      setRewards((prev) =>
        prev.map((r) => (r.id === data.rewardId ? updatedReward : r))
      );
      return updatedReward;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update reward');
      throw err;
    }
  };

  const toggleStatus = async (rewardId: number, isActive: boolean) => {
    try {
      const updatedReward = await rewardService.toggleRewardStatus(
        rewardId,
        isActive
      );
      setRewards((prev) =>
        prev.map((r) => (r.id === rewardId ? updatedReward.reward : r))
      );
      return updatedReward;
    } catch (err) {
      setError(
        err instanceof Error ? err.message : 'Failed to toggle reward status'
      );
      throw err;
    }
  };

  const deleteReward = async (rewardId: number) => {
    try {
      await rewardService.deleteReward(rewardId);
      setRewards((prev) => prev.filter((r) => r.id !== rewardId));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete reward');
      throw err;
    }
  };

  useEffect(() => {
    if (options.autoFetch) {
      fetchRewards(options.query);
    }
  }, [options.autoFetch]);

  return {
    rewards,
    loading,
    error,
    fetchRewards,
    createReward,
    updateReward,
    toggleStatus,
    deleteReward,
    rewardService,
  };
}
```

## Testing Examples

### Unit Tests (Jest)

```javascript
describe('RewardService', () => {
  let rewardService;

  beforeEach(() => {
    rewardService = new RewardService('test-token');
    global.fetch = jest.fn();
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  test('should create reward successfully', async () => {
    const mockReward = { id: 1, name: 'Test Reward' };
    global.fetch.mockResolvedValueOnce({
      json: () => Promise.resolve({ success: true, data: mockReward }),
    });

    const rewardData = {
      name: 'Test Reward',
      description: 'Test Description',
      eventType: 'TEST_EVENT',
      valueType: 'CASH',
      value: 100,
    };

    const result = await rewardService.createReward(rewardData);

    expect(global.fetch).toHaveBeenCalledWith('/api/v1/reward/create', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer test-token',
      },
      body: JSON.stringify(rewardData),
    });

    expect(result).toEqual(mockReward);
  });

  test('should handle update reward with new format', async () => {
    const mockUpdatedReward = { id: 1, name: 'Updated Reward' };
    global.fetch.mockResolvedValueOnce({
      json: () => Promise.resolve({ success: true, data: mockUpdatedReward }),
    });

    const updateData = {
      rewardId: 1, // Note: using rewardId, not id
      name: 'Updated Reward',
      value: 200,
    };

    const result = await rewardService.updateReward(updateData);

    expect(global.fetch).toHaveBeenCalledWith('/api/v1/reward/update', {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer test-token',
      },
      body: JSON.stringify(updateData),
    });

    expect(result).toEqual(mockUpdatedReward);
  });
});
```

### Integration Tests (Cypress)

```javascript
describe('Reward Management', () => {
  beforeEach(() => {
    cy.login('<EMAIL>', 'password');
    cy.visit('/rewards');
  });

  it('should create a new reward', () => {
    cy.get('[data-testid="create-reward-btn"]').click();

    cy.get('[data-testid="reward-name"]').type('Test Reward');
    cy.get('[data-testid="reward-description"]').type('Test Description');
    cy.get('[data-testid="event-type"]').select('INNOVATION_IDEA_POSTED');
    cy.get('[data-testid="value-type"]').select('CASH');
    cy.get('[data-testid="reward-value"]').type('500');

    cy.get('[data-testid="submit-btn"]').click();

    cy.get('[data-testid="success-message"]').should(
      'contain',
      'Reward created successfully'
    );
    cy.get('[data-testid="rewards-list"]').should('contain', 'Test Reward');
  });

  it('should update existing reward', () => {
    cy.get('[data-testid="reward-item"]:first').within(() => {
      cy.get('[data-testid="edit-btn"]').click();
    });

    cy.get('[data-testid="reward-name"]').clear().type('Updated Reward');
    cy.get('[data-testid="reward-value"]').clear().type('750');

    cy.get('[data-testid="submit-btn"]').click();

    cy.get('[data-testid="success-message"]').should(
      'contain',
      'Reward updated successfully'
    );
    cy.get('[data-testid="rewards-list"]').should('contain', 'Updated Reward');
  });

  it('should toggle reward status', () => {
    cy.get('[data-testid="reward-item"]:first').within(() => {
      cy.get('[data-testid="status-toggle"]').click();
    });

    cy.get('[data-testid="confirm-dialog"]').within(() => {
      cy.get('[data-testid="confirm-btn"]').click();
    });

    cy.get('[data-testid="success-message"]').should(
      'contain',
      'Status updated successfully'
    );
  });
});
```

## Verification Checklist

✅ **TypeScript Compilation:** All types are correct, no compilation errors ✅ **Application Startup:** Server starts successfully with all services ✅ **Route Registration:** All endpoints are properly registered ✅ **Service Integration:** All services work together correctly ✅ **Frontend Guide:** Comprehensive API documentation provided ✅ **Migration Path:** Clear breaking changes and migration steps ✅ **Code Examples:** TypeScript interfaces and helper functions ✅ **Testing Examples:** Unit and integration test templates

## Next Steps

1. **Update Frontend Code** using the provided migration guide and helper functions
2. **Implement Error Handling** using the recommended patterns
3. **Add Tests** using the provided test examples
4. **Update Documentation** in your frontend project README
5. **Train Team** on the new unified API structure

The reward system is now much cleaner, more maintainable, and provides a better developer experience while maintaining all the advanced functionality of the dynamic reward system. This comprehensive guide should help frontend developers quickly adapt to the new unified API structure.
