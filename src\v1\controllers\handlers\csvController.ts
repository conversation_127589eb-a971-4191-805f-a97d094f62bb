import { Response } from 'express';
import { HttpError } from '../../utils/httpError';
import { logger } from '../../utils/logger';

type CSVOperationHandler<T> = (
  userId: any,
  params: any,
  reqBody: any,
  query?: any
) => Promise<T>;

interface CSVExportResult {
  content: string;
  filename: string;
  contentType: string;
  headers: Record<string, string>;
}

/**
 * Controller handler that can handle both regular JSON responses and CSV exports
 * @param handler - The service handler function
 * @param params - Request parameters
 * @param res - Express response object
 * @param userId - User ID
 * @param reqBody - Request body
 * @param query - Query parameters
 */
export const csvControllerOperations = async <T>(
  handler: CSVOperationHandler<T>,
  params: any,
  res: Response,
  userId?: string | number,
  reqBody?: any,
  query?: any
) => {
  try {
    const result = await handler(userId, params, query, reqBody);

    // Check if result is a CSV export
    if (
      result &&
      typeof result === 'object' &&
      'content' in result &&
      'filename' in result
    ) {
      const csvResult = result as unknown as CSVExportResult;

      // Set CSV headers
      res.set(csvResult.headers);

      // Send CSV content
      return res.status(200).send(csvResult.content);
    }

    // Regular JSON response
    return res.status(200).json({ message: 'Successful', data: result });
  } catch (error) {
    if (error instanceof HttpError) {
      return res.status(error.statusCode).json({
        message: error.message,
        data: null,
      });
    } else if (error instanceof Error) {
      return res.status(400).json({ message: error.message, data: null });
    } else {
      logger.error('An unknown error occurred');
    }

    return res.status(500).json({
      message: 'Internal server error',
      data: null,
    });
  }
};
