import { db } from '../../utils/model';
import { HttpError } from '../../utils/httpError';
import { logger } from '../../utils/logger';
import {
  getCache,
  setCache,
  deleteCache,
  deleteCacheByPattern,
} from '../../utils/cache';
import { staffHasPermission, PERMISSIONS } from '../../utils/permission';
// import { ForumNotificationService } from './forumNotificationService';
// import { GameNotificationService } from './gameNotificationService';
import { SocketNotificationService } from './socketNotificationService';

export interface NotificationFilters {
  type?: string;
  priority?: 'low' | 'medium' | 'high';
  isRead?: boolean;
  startDate?: Date;
  endDate?: Date;
}

export interface NotificationPreferences {
  emailNotifications: boolean;
  pushNotifications: boolean;
  forumNotifications: boolean;
  gameNotifications: boolean;
  mentionNotifications: boolean;
}

// Helper function to clear all notification-related caches
export const clearNotificationCaches = async (): Promise<void> => {
  await deleteCacheByPattern('notifications:*');
};

export const notificationService = {
  getUserNotifications: async (staffId: number, query: any) => {
    try {
      const page = parseInt(query.page) || 1;
      const limit = parseInt(query.limit) || 20;
      const skip = (page - 1) * limit;

      const whereClause: any = {
        staffId,
        ...(query.type ? { type: query.type } : {}),
        ...(query.priority ? { priority: query.priority } : {}),
        ...(query.isRead !== undefined
          ? { isRead: query.isRead === 'true' }
          : {}),
        ...(query.startDate || query.endDate
          ? {
              createdAt: {
                ...(query.startDate ? { gte: new Date(query.startDate) } : {}),
                ...(query.endDate ? { lte: new Date(query.endDate) } : {}),
              },
            }
          : {}),
      };

      const [notifications, total] = await Promise.all([
        db.notification.findMany({
          where: whereClause,
          orderBy: { createdAt: 'desc' },
          skip,
          take: limit,
          select: {
            id: true,
            type: true,
            title: true,
            message: true,
            data: true,
            priority: true,
            isRead: true,
            createdAt: true,
          },
        }),
        db.notification.count({ where: whereClause }),
      ]);

      const totalPages = Math.ceil(total / limit);

      return {
        notifications: notifications.map((notification) => ({
          ...notification,
          data: notification.data ? JSON.parse(notification.data) : null,
        })),
        pagination: {
          page,
          limit,
          total,
          totalPages,
        },
      };
    } catch (error) {
      logger.error('Failed to get notifications', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to get notifications', 400);
    }
  },

  // Mark notifications as read
  markAsRead: async (staffId: number, notificationId: string) => {
    try {
      const result = await db.notification.updateMany({
        where: {
          id: notificationId,
          staffId,
        },
        data: {
          isRead: true,
          readAt: new Date(),
        },
      });

      // Emit socket notification for read status update
      SocketNotificationService.notifyUser(
        staffId,
        'notifications_marked_read',
        {
          title: 'Notifications Updated',
          message: `${result.count} notifications marked as read`,
          type: 'info',
          data: { count: result.count, notificationId },
        }
      );

      logger.info(
        `Marked ${result.count} notifications as read for user ${staffId}`
      );
      return result;
    } catch (error) {
      logger.error('Failed to mark notifications as read', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to mark notifications as read', 400);
    }
  },

  // Mark all notifications as read for a user
  markAllAsRead: async (staffId: number) => {
    try {
      const result = await db.notification.updateMany({
        where: {
          staffId,
          isRead: false,
        },
        data: {
          isRead: true,
          readAt: new Date(),
        },
      });

      // Emit socket notification for all read
      SocketNotificationService.notifyUser(
        staffId,
        'all_notifications_marked_read',
        {
          title: 'All Notifications Read',
          message: `All ${result.count} notifications marked as read`,
          type: 'success',
          data: { count: result.count },
        }
      );

      logger.info(
        `Marked all ${result.count} notifications as read for user ${staffId}`
      );
      return result;
    } catch (error) {
      logger.error('Failed to mark all notifications as read', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to mark all notifications as read', 400);
    }
  },

  // Get unread notification count
  getUnreadCount: async (staffId: number) => {
    try {
      const count = await db.notification.count({
        where: {
          staffId,
          isRead: false,
        },
      });

      return { count };
    } catch (error) {
      logger.error('Failed to get unread notification count', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to get unread notification count', 400);
    }
  },

  // Delete notifications
  deleteNotifications: async (staffId: number, notificationId: string) => {
    try {
      const result = await db.notification.delete({
        where: {
          id: notificationId,
          staffId,
        },
      });

      logger.info(
        `Deleted ${result.message} notifications for user ${staffId}`
      );
      return result;
    } catch (error) {
      logger.error('Failed to delete notifications', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to delete notifications', 400);
    }
  },

  deleteAllNotifications: async (staffId: number) => {
    try {
      const result = await db.notification.deleteMany({
        where: {
          staffId,
        },
      });

      logger.info(`Deleted ${result.count} notifications for user ${staffId}`);
      return result;
    } catch (error) {
      logger.error('Failed to delete all notifications', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to delete all notifications', 400);
    }
  },

  // Get notification preferences for a user
  getNotificationPreferences: async (
    staffId: number
  ): Promise<NotificationPreferences> => {
    try {
      const preferences = await db.notificationPreference.findUnique({
        where: { staffId },
      });

      if (!preferences) {
        // Return default preferences
        return {
          emailNotifications: true,
          pushNotifications: true,
          forumNotifications: true,
          gameNotifications: true,
          mentionNotifications: true,
        };
      }

      return {
        emailNotifications: preferences.emailNotifications,
        pushNotifications: preferences.pushNotifications,
        forumNotifications: preferences.forumNotifications,
        gameNotifications: preferences.gameNotifications,
        mentionNotifications: preferences.mentionNotifications,
      };
    } catch (error) {
      logger.error('Failed to get notification preferences', error);
      throw new HttpError('Failed to get notification preferences', 400);
    }
  },

  // Update notification preferences for a user
  updateNotificationPreferences: async (
    staffId: number,
    reqBody: Partial<NotificationPreferences>
  ) => {
    try {
      // Validate preferences
      const validKeys = [
        'emailNotifications',
        'pushNotifications',
        'forumNotifications',
        'gameNotifications',
        'mentionNotifications',
      ];

      const filteredPreferences: any = {};
      for (const key of validKeys) {
        if (
          key in reqBody &&
          typeof reqBody[key as keyof NotificationPreferences] === 'boolean'
        ) {
          filteredPreferences[key] =
            reqBody[key as keyof NotificationPreferences];
        }
      }

      if (Object.keys(filteredPreferences).length === 0) {
        throw new HttpError('No valid preferences provided', 400);
      }

      const result = await db.notificationPreference.upsert({
        where: { staffId },
        update: filteredPreferences,
        create: {
          staffId,
          ...filteredPreferences,
        },
      });

      logger.info(`Updated notification preferences for user ${staffId}`);
      return result;
    } catch (error) {
      logger.error('Failed to update notification preferences', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to update notification preferences', 400);
    }
  },

  // Get notification statistics
  getNotificationStats: async (staffId: number) => {
    try {
      const [total, unread, byType, byPriority] = await Promise.all([
        db.notification.count({
          where: { staffId },
        }),
        db.notification.count({
          where: { staffId, isRead: false },
        }),
        db.notification.groupBy({
          by: ['type'],
          where: { staffId },
          _count: { type: true },
        }),
        db.notification.groupBy({
          by: ['priority'],
          where: { staffId },
          _count: { priority: true },
        }),
      ]);

      return {
        total,
        unread,
        read: total - unread,
        byType: byType.map((item) => ({
          type: item.type,
          count: item._count.type,
        })),
        byPriority: byPriority.map((item) => ({
          priority: item.priority,
          count: item._count.priority,
        })),
      };
    } catch (error) {
      logger.error('Failed to get notification statistics', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to get notification statistics', 400);
    }
  },

  // Clean up old notifications (older than 30 days)
  cleanupOldNotifications: async (staffId: number) => {
    try {
      await staffHasPermission(staffId, PERMISSIONS.STAFF_EDIT);

      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const result = await db.notification.deleteMany({
        where: {
          createdAt: {
            lt: thirtyDaysAgo,
          },
          isRead: true,
        },
      });

      logger.info(`Cleaned up ${result.count} old notifications`);
      return result;
    } catch (error) {
      logger.error('Failed to clean up old notifications', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to clean up old notifications', 400);
    }
  },

  // Get recent activity for dashboard
  getRecentActivity: async (staffId: number, query: any) => {
    try {
      const limit = parseInt(query.limit) || 10;

      const notifications = await db.notification.findMany({
        where: { staffId },
        orderBy: { createdAt: 'desc' },
        take: limit,
        select: {
          id: true,
          type: true,
          title: true,
          message: true,
          data: true,
          priority: true,
          isRead: true,
          createdAt: true,
        },
      });

      return notifications.map((notification) => ({
        ...notification,
        data: notification.data ? JSON.parse(notification.data) : null,
      }));
    } catch (error) {
      logger.error('Failed to get recent activity', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to get recent activity', 400);
    }
  },

  // Create notification with socket emission
  createNotificationWithSocket: async (
    userId: number,
    title: string,
    message: string,
    type: string = 'system',
    priority: 'low' | 'medium' | 'high' = 'medium',
    data?: any
  ) => {
    try {
      const notification = await db.notification.create({
        data: {
          staffId: userId,
          type,
          title,
          message,
          priority,
          data: data ? JSON.stringify(data) : null,
          isRead: false,
        },
      });

      // Emit socket notification
      SocketNotificationService.notifyUser(userId, 'new_notification', {
        title,
        message,
        type:
          priority === 'high'
            ? 'warning'
            : priority === 'low'
              ? 'info'
              : 'success',
        data: {
          notificationId: notification.id,
          type,
          priority,
          ...data,
        },
      });

      logger.info(`Notification created and sent to user ${userId}: ${title}`);
      return notification;
    } catch (error) {
      logger.error('Failed to create notification', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to create notification', 400);
    }
  },

  // Bulk operations for admin
  sendBulkNotification: async (
    staffId: number,
    reqBody: {
      userIds: number[];
      title: string;
      message: string;
      type?: string;
      priority?: 'low' | 'medium' | 'high';
    }
  ) => {
    try {
      await staffHasPermission(staffId, PERMISSIONS.STAFF_EDIT);

      const {
        userIds,
        title,
        message,
        type = 'system',
        priority = 'medium',
      } = reqBody;

      if (!userIds || !Array.isArray(userIds) || userIds.length === 0) {
        throw new HttpError(
          'User IDs are required and must be a non-empty array',
          400
        );
      }

      if (!title || !message) {
        throw new HttpError('Title and message are required', 400);
      }

      const notifications = userIds.map((userId) => ({
        staffId: userId,
        type,
        title,
        message,
        priority,
        isRead: false,
      }));

      const result = await db.notification.createMany({
        data: notifications,
      });

      // Emit socket notifications to all users
      SocketNotificationService.notifyMultipleUsers(
        userIds,
        'new_notification',
        {
          title,
          message,
          type: priority === 'high' ? 'warning' : 'info',
          data: { type, priority },
        }
      );

      logger.info(`Sent bulk notification to ${userIds.length} users`);
      return result;
    } catch (error) {
      logger.error('Failed to send bulk notification', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to send bulk notification', 400);
    }
  },
};
