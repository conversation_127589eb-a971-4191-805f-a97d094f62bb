import { db } from '../../utils/model';
import { HttpError } from '../../utils/httpError';
import { formatString } from '../../utils/stringFormatter';
import { staffHasPermission, PERMISSIONS } from '../../utils/permission';
import { logger } from '../../utils/logger';

export const categoryService = {
  getAllCategoriesAdmin: async (staffId: any, query: any) => {
    try {
      await staffHasPermission(staffId, PERMISSIONS.PACKAGE_VIEW);
      const page: number = parseInt(query.page as string);
      const limit: number = parseInt(query.limit as string);
      const [categories, totalPages] = await db.$transaction([
        db.packageCategory.findMany({
          orderBy: { name: 'desc' },
          skip: (page - 1) * limit,
          take: limit,
          select: {
            id: true,
            name: true,
            slug: true,
            createdBy: true,
            isActive: true,
            createdAt: true,
            updatedAt: true,
            updatedBy: true,
            _count: {
              select: {
                packages: true,
              },
            },
          },
        }),

        db.packageCategory.count(),
      ]);

      const categoriesWithPackageCount = categories.map((category) => ({
        ...category,
        packagesCount: category._count.packages,
      }));

      return {
        categories: categoriesWithPackageCount,
        totalPages: Math.ceil(totalPages / limit),
        totalCount: totalPages,
      };
    } catch (error) {
      logger.error('Error getting all categories:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to fetch categories', 400);
    }
  },

  getAllCategoriesPublic: async () => {
    try {
      return db.packageCategory.findMany({
        select: {
          id: true,
          name: true,
          slug: true,
        },
      });
    } catch (error) {
      logger.error('Error getting all categories:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to fetch categories', 400);
    }
  },

  getCategoryBySlug: async (slug: string, query: any) => {
    try {
      const category = await db.packageCategory.findFirst({
        where: { slug: slug },
        include: {
          packages: true,
        },
      });
      if (!category) {
        throw new HttpError('Category not found', 404);
      }
      return category;
    } catch (error) {
      logger.error('Error getting category by slug:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to fetch category', 400);
    }
  },

  getCategoryById: async (id: number, query: any) => {
    try {
      const category = await db.packageCategory.findUnique({
        where: { id: Number(id) },
      });
      return category;
    } catch (error) {
      logger.error('Error getting category by id:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to fetch category', 400);
    }
  },

  createCategory: async (staffId: any, reqBody: any) => {
    try {
      await staffHasPermission(staffId, PERMISSIONS.PACKAGE_CREATE);

      const checkCategory = await db.packageCategory.findUnique({
        where: { name: reqBody.name },
      });
      if (checkCategory) {
        throw new HttpError('Category with the title already exists', 400);
      }
      await db.packageCategory.create({
        data: {
          slug: formatString.formatSlug(reqBody.name),
          ...reqBody,
        },
      });
      return {
        message: `${reqBody.name} - Package category created successfully`,
      };
    } catch (error) {
      logger.error('Error creating category:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to create category', 400);
    }
  },

  updateCategory: async (staffId: any, reqBody: any) => {
    try {
      await staffHasPermission(staffId, PERMISSIONS.PACKAGE_EDIT);

      const categoryId = Number(reqBody.id);
      const newName = reqBody.name;

      const existingCategory = await db.packageCategory.findUnique({
        where: { id: categoryId },
      });

      if (!existingCategory) {
        throw new HttpError('Category not found', 404);
      }

      const categoryWithSameName = await db.packageCategory.findUnique({
        where: { name: newName },
      });

      if (
        categoryWithSameName &&
        categoryWithSameName.id !== existingCategory.id
      ) {
        throw new HttpError('Category name already exists', 400);
      }

      const updateData = { ...reqBody };
      if (
        categoryWithSameName &&
        categoryWithSameName.id === existingCategory.id
      ) {
        delete updateData.name;
      }

      await db.packageCategory.update({
        where: { id: categoryId },
        data: updateData,
      });

      return { message: 'Category updated successfully' };
    } catch (error) {
      logger.error('Error updating category:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to update category', 400);
    }
  },

  deleteCategory: async (staffId: any, categoryId: number) => {
    try {
      await staffHasPermission(staffId, PERMISSIONS.PACKAGE_DELETE);

      const checkCategory = await db.packageCategory.findUnique({
        where: { id: Number(categoryId) },
      });
      if (!checkCategory) {
        throw new HttpError('Category not found', 404);
      }
      await db.packageCategory.delete({
        where: { id: Number(categoryId) },
      });
      return { message: 'Category deleted successfully' };
    } catch (error) {
      logger.error('Error deleting category:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to delete category', 400);
    }
  },
};
