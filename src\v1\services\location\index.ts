import { db } from '../../utils/model';
import { HttpError } from '../../utils/httpError';
import { staffHasPermission, PERMISSIONS } from '../../utils/permission';
import { logger } from '../../utils/logger';

export const locationService = {
  getAllLocations: async () => {
    try {
      return await db.location.findMany({
        include: {
          region: true,
        },
      });
    } catch (error) {
      logger.error('Failed to get locations', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to get locations', 400);
    }
  },

  createLocation: async (staffId: any, reqBody: any) => {
    try {
      await staffHasPermission(staffId, PERMISSIONS.LOCATION_CREATE);

      const checkLocation = await db.location.findFirst({
        where: { name: reqBody.name },
      });
      if (checkLocation) {
        throw new HttpError('Location already exists', 400);
      }
      await db.location.create({
        data: {
          ...reqBody,
        },
      });
      return {
        message: `${reqBody.name} - location created successfully`,
      };
    } catch (error) {
      logger.error('Failed to create location', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to create location', 400);
    }
  },

  updateLocation: async (staffId: any, reqBody: any) => {
    try {
      const updateData = { ...reqBody };
      await staffHasPermission(staffId, PERMISSIONS.LOCATION_EDIT);

      const checkLocation = await db.location.findUnique({
        where: { id: Number(reqBody.id) },
      });
      if (checkLocation && checkLocation.name !== reqBody.name) {
        return db.location.update({
          where: { id: reqBody.id },
          data: updateData,
        });
      } else {
        throw new HttpError('Edit location not allowed', 404);
      }
    } catch (error) {
      logger.error('Failed to update location', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to update location', 400);
    }
  },

  deleteLocation: async (staffId: any, locationId: number) => {
    try {
      await staffHasPermission(staffId, PERMISSIONS.LOCATION_DELETE);

      const checkLocation = await db.location.findUnique({
        where: { id: Number(locationId) },
      });
      if (!checkLocation) {
        throw new HttpError('Location does not exist', 404);
      }
      await db.location.delete({
        where: { id: Number(locationId) },
      });
      return { message: 'Location deleted successfully' };
    } catch (error) {
      logger.error('Failed to delete location', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to delete location', 400);
    }
  },
};
