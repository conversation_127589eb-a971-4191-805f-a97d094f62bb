import { Router } from 'express';
import { secure } from '../../middleware/auth';
import { suggestionControllers } from '../../controllers/suggestion/suggestion.controller';

export const suggestionRoute = Router();

suggestionRoute.post(
  '/create',
  secure,
  suggestionControllers.CreateSuggestionHandler
);

suggestionRoute.get(
  '/list',
  secure,
  suggestionControllers.ListSuggestionsHandler
);

suggestionRoute.patch(
  '/update',
  secure,
  suggestionControllers.UpdateSuggestionHandler
);

suggestionRoute.delete(
  '/:suggestionId',
  secure,
  suggestionControllers.DeleteSuggestionHandler
);
