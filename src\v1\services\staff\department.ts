import { db } from '../../utils/model';
import { HttpError } from '../../utils/httpError';
import { formatString } from '../../utils/stringFormatter';
import { staffHasPermission, PERMISSIONS } from '../../utils/permission';
import { logger } from '../../utils/logger';

interface UpdateDepartmentRequestBody {
  id: string | number;
  name?: string;
  managerId?: string | number | null;
}

export const departmentService = {
  getAllDepartment: async (staffId: any, query: any) => {
    try {
      await staffHasPermission(staffId, PERMISSIONS.STAFF_VIEW);
      const search = query.search as string;

      return db.department.findMany({
        where: {
          name: {
            contains: search,
            mode: 'insensitive',
          },
        },
        include: {
          units: {
            include: {
              staff: {
                select: {
                  fullName: true,
                },
              },
            },
          },
          manager: {
            select: {
              fullName: true,
            },
          },
        },
      });
    } catch (error) {
      logger.error(`Error in getAllDepartment: ${error}`);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to fetch departments', 400);
    }
  },

  createDepartment: async (staffId: any, reqBody: any) => {
    try {
      await staffHasPermission(staffId, PERMISSIONS.STAFF_CREATE);
      const { name } = reqBody;
      const formattedString = formatString.trimString(name);

      const checkDepartment = await db.department.findFirst({
        where: { name: formattedString },
      });

      if (checkDepartment) {
        throw new HttpError('Department already exists', 400);
      }
      await db.department.create({
        data: {
          name: formattedString,
        },
      });
      return {
        message: 'Department created successfully',
      };
    } catch (error) {
      logger.error(`Error in createDepartment: ${error}`);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to create department', 400);
    }
  },

  createUnit: async (staffId: any, reqBody: any) => {
    try {
      await staffHasPermission(staffId, PERMISSIONS.STAFF_CREATE);
      const { name, departmentId } = reqBody;
      const formattedString = formatString.trimString(name);

      const checkUnit = await db.unit.findFirst({
        where: { name: formattedString },
      });

      if (checkUnit) {
        throw new HttpError('Unit already exists', 400);
      }
      await db.unit.create({
        data: {
          name: formattedString,
          departmentId: departmentId,
        },
      });
      return {
        message: 'Unit created successfully',
      };
    } catch (error) {
      logger.error(`Error in createUnit: ${error}`);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to create unit', 400);
    }
  },

  updateDepartment: async (
    staffId: any,
    reqBody: UpdateDepartmentRequestBody
  ) => {
    try {
      await staffHasPermission(staffId, PERMISSIONS.STAFF_EDIT);

      const { id, name, managerId: newManagerIdValue } = reqBody;

      if (id === undefined || id === null) {
        throw new HttpError('Department ID is required for update', 400);
      }

      const departmentId = Number(id);
      if (isNaN(departmentId)) {
        throw new HttpError('Invalid department ID format', 400);
      }

      const department = await db.department.findUnique({
        where: { id: departmentId },
      });

      if (!department) {
        throw new HttpError(`Department with not found`, 404);
      }

      const updateData: { name?: string; managerId?: number | null } = {};
      let hasChanges = false;

      if (name !== undefined && name !== null) {
        const formattedName = formatString.trimString(name);
        if (formattedName === '') {
          throw new HttpError('Department name cannot be empty', 400);
        }
        if (formattedName !== department.name) {
          const existingDepartmentWithName = await db.department.findFirst({
            where: { name: formattedName, NOT: { id: departmentId } },
          });
          if (existingDepartmentWithName) {
            throw new HttpError(
              'Another department with this name already exists',
              400
            );
          }
          updateData.name = formattedName;
          hasChanges = true;
        }
      }

      if (reqBody.hasOwnProperty('managerId')) {
        const newManagerId = Number(newManagerIdValue);
        if (newManagerIdValue !== null && newManagerIdValue !== undefined) {
          throw new HttpError('Invalid manager ID format', 400);
        }

        if (department.managerId !== newManagerId) {
          if (newManagerId !== null) {
            const staffExists = await db.staff.findUnique({
              where: { id: newManagerId },
            });
            if (!staffExists) {
              throw new HttpError(
                `Manager (staff) with ID ${newManagerId} does not exist`,
                404
              );
            }
          }
          updateData.managerId = newManagerId;
          hasChanges = true;
        }
      }

      if (!hasChanges) {
        throw new HttpError(
          'No changes provided or new values are the same as current values',
          400
        );
      }

      const updatedDepartment = await db.department.update({
        where: { id: departmentId },
        data: updateData,
      });

      return {
        message: 'Department updated successfully',
        department: updatedDepartment,
      };
    } catch (error) {
      logger.error(`Error in updateDepartment: ${error}`);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to update department', 400);
    }
  },
};
