import { Request, Response } from 'express';
import { controllerOperations } from '../handlers/handleController';
import { notificationService } from '../../services/notification';
import { staffHasPermission, PERMISSIONS } from '../../utils/permission';

const GetNotificationsHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    notificationService.getUserNotifications,
    req.query,
    res,
    staffId
  );
};

const GetUnreadCountHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    notificationService.getUnreadCount,
    undefined,
    res,
    staffId
  );
};

const GetStatsHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    notificationService.getNotificationStats,
    undefined,
    res,
    staffId
  );
};

const GetRecentActivityHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    notificationService.getRecentActivity,
    req.query,
    res,
    staffId
  );
};

const GetPreferencesHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    notificationService.getNotificationPreferences,
    undefined,
    res,
    staffId
  );
};

const UpdatePreferencesHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    notificationService.updateNotificationPreferences,
    req.body,
    res,
    staffId
  );
};

const MarkAsReadHandler = (req: Request, res: Response) => {
  const { notificationId } = req.params;
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    notificationService.markAsRead,
    notificationId,
    res,
    staffId
  );
};

const MarkAllAsReadHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    notificationService.markAllAsRead,
    undefined,
    res,
    staffId
  );
};

const DeleteNotificationsHandler = (req: Request, res: Response) => {
  const { notificationId } = req.params;
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    notificationService.deleteNotifications,
    notificationId,
    res,
    staffId
  );
};

const DeleteAllNotificationsHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    notificationService.deleteAllNotifications,
    undefined,
    res,
    staffId
  );
};

const CleanupOldNotificationsHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    notificationService.cleanupOldNotifications,
    undefined,
    res,
    staffId
  );
};

const SendBulkNotificationHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    notificationService.sendBulkNotification,
    req.body,
    res,
    staffId
  );
};

export const notificationControllers = {
  GetNotificationsHandler,
  GetUnreadCountHandler,
  GetStatsHandler,
  GetRecentActivityHandler,
  GetPreferencesHandler,
  UpdatePreferencesHandler,
  MarkAsReadHandler,
  MarkAllAsReadHandler,
  DeleteNotificationsHandler,
  DeleteAllNotificationsHandler,
  CleanupOldNotificationsHandler,
  SendBulkNotificationHandler,
};
