# Simple Reward System Proposal

## 🎯 **Core Philosophy: Keep It Simple**

Instead of the current complex system with rules, conditions, and multiple tables, let's create a simple, intuitive reward system that matches your actual needs.

## 📊 **Simplified Database Schema**

### Single Reward Table

```prisma
model Reward {
  id                            Int                           @id @default(autoincrement())
  createdAt                     DateTime                      @default(now())
  updatedAt                     DateTime                      @updatedAt

  // Basic Info
  name                          String                        @unique
  description                   String?
  isActive                      Boolean                       @default(true)

  // What triggers this reward
  eventType                     RewardEventType               // INNOVATION_IDEA_POSTED, INNOVATION_COMMENT, PACKAGE_REFERRAL

  // What the user gets
  valueType                     RewardValueType               // CASH, POINTS, VOUCHER
  value                         Float                         // Amount to give

  // Optional restrictions
  locationId                    Int?                          // null = global, specific = location only
  maxPerUser                    Int?                          // null = unlimited, number = max times per user
  maxPerDay                     Int?                          // null = unlimited, number = max times per day

  // Tracking
  rewardLogs                    RewardLog[]
  location                      Location?                     @relation(fields: [locationId], references: [id])
}

model RewardLog {
  id                            Int                           @id @default(autoincrement())
  createdAt                     DateTime                      @default(now())

  staffId                       Int
  rewardId                      Int
  value                         Float
  valueType                     RewardValueType
  eventType                     RewardEventType

  // Context (what triggered it)
  entityType                    String?                       // "innovation_idea", "comment", "booking"
  entityId                      Int?                          // ID of the thing that triggered it

  staff                         Staff                         @relation(fields: [staffId], references: [id])
  reward                        Reward                        @relation(fields: [rewardId], references: [id])
}
```

## 🔧 **Simplified Service**

### Single Reward Service

```typescript
// src/v1/services/reward/simpleReward.ts
export const simpleRewardService = {
  // Admin functions
  createReward: async (staffId: number, data: CreateRewardData) => {
    await staffHasPermission(staffId, PERMISSIONS.REWARD_CREATE);

    return db.reward.create({
      data: {
        name: data.name,
        description: data.description,
        eventType: data.eventType,
        valueType: data.valueType,
        value: data.value,
        locationId: data.locationId,
        maxPerUser: data.maxPerUser,
        maxPerDay: data.maxPerDay,
      },
    });
  },

  listRewards: async (staffId: number, query: any) => {
    await staffHasPermission(staffId, PERMISSIONS.REWARD_VIEW);

    return db.reward.findMany({
      where: {
        isActive: query.isActive === 'false' ? false : true,
        ...(query.eventType && { eventType: query.eventType }),
        ...(query.locationId && { locationId: parseInt(query.locationId) }),
      },
      include: {
        location: true,
        _count: {
          select: { rewardLogs: true },
        },
      },
    });
  },

  updateReward: async (
    staffId: number,
    rewardId: number,
    data: UpdateRewardData
  ) => {
    await staffHasPermission(staffId, PERMISSIONS.REWARD_EDIT);

    return db.reward.update({
      where: { id: rewardId },
      data,
    });
  },

  deleteReward: async (staffId: number, rewardId: number) => {
    await staffHasPermission(staffId, PERMISSIONS.REWARD_DELETE);

    return db.reward.delete({
      where: { id: rewardId },
    });
  },

  // The main reward trigger function
  triggerReward: async (
    eventType: RewardEventType,
    staffId: number,
    entityType?: string,
    entityId?: number
  ) => {
    try {
      // Get staff location
      const staff = await db.staff.findUnique({
        where: { id: staffId },
        select: { locationId: true },
      });

      // Find active rewards for this event
      const rewards = await db.reward.findMany({
        where: {
          eventType,
          isActive: true,
          OR: [
            { locationId: null }, // Global rewards
            { locationId: staff?.locationId }, // Location-specific rewards
          ],
        },
      });

      const results = [];

      for (const reward of rewards) {
        // Check if user has exceeded limits
        if (
          await simpleRewardService.canReceiveReward(
            staffId,
            reward.id,
            reward.maxPerUser,
            reward.maxPerDay
          )
        ) {
          const rewardGiven = await simpleRewardService.giveReward(
            staffId,
            reward,
            entityType,
            entityId
          );
          results.push(rewardGiven);
        }
      }

      return results;
    } catch (error) {
      logger.error('Error triggering reward:', error);
      return [];
    }
  },

  // Check if user can receive reward (respects limits)
  canReceiveReward: async (
    staffId: number,
    rewardId: number,
    maxPerUser?: number,
    maxPerDay?: number
  ) => {
    if (maxPerUser) {
      const userCount = await db.rewardLog.count({
        where: { staffId, rewardId },
      });
      if (userCount >= maxPerUser) return false;
    }

    if (maxPerDay) {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);

      const todayCount = await db.rewardLog.count({
        where: {
          rewardId,
          createdAt: {
            gte: today,
            lt: tomorrow,
          },
        },
      });
      if (todayCount >= maxPerDay) return false;
    }

    return true;
  },

  // Actually give the reward to the user
  giveReward: async (
    staffId: number,
    reward: any,
    entityType?: string,
    entityId?: number
  ) => {
    // Update staff balance/points/vouchers
    if (reward.valueType === 'CASH') {
      await db.staff.update({
        where: { id: staffId },
        data: { balance: { increment: reward.value } },
      });
    } else if (reward.valueType === 'POINTS') {
      await db.staff.update({
        where: { id: staffId },
        data: { pointsAccrued: { increment: reward.value } },
      });
    } else if (reward.valueType === 'VOUCHER') {
      await db.staff.update({
        where: { id: staffId },
        data: { mealVoucher: { increment: reward.value } },
      });
    }

    // Log the reward
    const rewardLog = await db.rewardLog.create({
      data: {
        staffId,
        rewardId: reward.id,
        value: reward.value,
        valueType: reward.valueType,
        eventType: reward.eventType,
        entityType,
        entityId,
      },
    });

    logger.info(
      `Rewarded staff ${staffId} with ${reward.value} ${reward.valueType} for ${reward.eventType}`
    );
    return rewardLog;
  },

  // Get reward history
  getRewardHistory: async (staffId: number, query: any) => {
    await staffHasPermission(staffId, PERMISSIONS.REWARD_VIEW);

    return db.rewardLog.findMany({
      where: {
        ...(query.targetStaffId && { staffId: parseInt(query.targetStaffId) }),
        ...(query.eventType && { eventType: query.eventType }),
        ...(query.startDate &&
          query.endDate && {
            createdAt: {
              gte: new Date(query.startDate),
              lte: new Date(query.endDate),
            },
          }),
      },
      include: {
        staff: { select: { fullName: true, email: true } },
        reward: { select: { name: true, description: true } },
      },
      orderBy: { createdAt: 'desc' },
      take: query.limit ? parseInt(query.limit) : 50,
      skip: query.page ? (parseInt(query.page) - 1) * (query.limit || 50) : 0,
    });
  },
};
```

## 🎯 **Simple Trigger Functions**

```typescript
// src/v1/services/reward/triggers.ts
export const rewardTriggers = {
  onInnovationIdeaPosted: (staffId: number, ideaId: number) =>
    simpleRewardService.triggerReward(
      'INNOVATION_IDEA_POSTED',
      staffId,
      'innovation_idea',
      ideaId
    ),

  onInnovationComment: (staffId: number, commentId: number) =>
    simpleRewardService.triggerReward(
      'INNOVATION_COMMENT',
      staffId,
      'comment',
      commentId
    ),

  onPackageReferral: (staffId: number, bookingId: number) =>
    simpleRewardService.triggerReward(
      'PACKAGE_REFERRAL',
      staffId,
      'booking',
      bookingId
    ),
};
```

## 🎮 **Simple Controller**

```typescript
// src/v1/controllers/reward/reward.controller.ts
export const rewardControllers = {
  // Admin endpoints
  listRewards: (req: Request, res: Response) => {
    const staffId = req.Staff as number;
    controllerOperations(
      simpleRewardService.listRewards,
      req.query,
      res,
      staffId
    );
  },

  createReward: (req: Request, res: Response) => {
    const staffId = req.Staff as number;
    controllerOperations(
      simpleRewardService.createReward,
      req.body,
      res,
      staffId
    );
  },

  updateReward: (req: Request, res: Response) => {
    const staffId = req.Staff as number;
    const { rewardId, ...data } = req.body;
    controllerOperations(
      simpleRewardService.updateReward,
      data,
      res,
      staffId,
      rewardId
    );
  },

  deleteReward: (req: Request, res: Response) => {
    const staffId = req.Staff as number;
    const { rewardId } = req.params;
    controllerOperations(
      simpleRewardService.deleteReward,
      undefined,
      res,
      staffId,
      parseInt(rewardId)
    );
  },

  // History endpoint
  getRewardHistory: (req: Request, res: Response) => {
    const staffId = req.Staff as number;
    controllerOperations(
      simpleRewardService.getRewardHistory,
      req.query,
      res,
      staffId
    );
  },
};
```

## 🛣️ **Simple Routes**

```typescript
// src/v1/routes/router/reward.route.ts
rewardRoute.get('/list', secure, rewardControllers.listRewards);
rewardRoute.post('/create', secure, rewardControllers.createReward);
rewardRoute.patch('/update', secure, rewardControllers.updateReward);
rewardRoute.delete('/:rewardId', secure, rewardControllers.deleteReward);
rewardRoute.get('/history', secure, rewardControllers.getRewardHistory);
```

## ✨ **Usage Examples**

### Creating Rewards (Admin)

```javascript
// Reward for posting innovation ideas
POST /api/v1/reward/create
{
  "name": "Innovation Idea Reward",
  "description": "Reward for posting innovation ideas",
  "eventType": "INNOVATION_IDEA_POSTED",
  "valueType": "CASH",
  "value": 500,
  "maxPerUser": 10  // Max 10 rewards per user
}

// Reward for commenting
POST /api/v1/reward/create
{
  "name": "Comment Reward",
  "eventType": "INNOVATION_COMMENT",
  "valueType": "POINTS",
  "value": 50,
  "maxPerDay": 100  // Max 100 comments rewarded per day
}

// Reward for referrals
POST /api/v1/reward/create
{
  "name": "Referral Reward",
  "eventType": "PACKAGE_REFERRAL",
  "valueType": "CASH",
  "value": 1000,
  "maxPerUser": 5  // Max 5 referrals per user
}
```

### Automatic Triggering (in your existing code)

```javascript
// In innovation idea service
await rewardTriggers.onInnovationIdeaPosted(staffId, ideaId);

// In comment service
await rewardTriggers.onInnovationComment(staffId, commentId);

// In package booking service (when referral code used)
await rewardTriggers.onPackageReferral(referrerStaffId, bookingId);
```

## 🎉 **Benefits of This Approach**

### ✅ **Dramatically Simpler**

- **2 tables** instead of 4 (Reward, RewardLog)
- **1 service** instead of 5 services
- **No complex rules, conditions, or JSON configurations**
- **Direct, intuitive API**

### ✅ **Covers Your Use Cases Perfectly**

- ✅ Set reward for posting innovation idea
- ✅ Set reward for posting comment
- ✅ Set reward for package booking with referral code
- ✅ Location-specific or global rewards
- ✅ Usage limits (per user, per day)
- ✅ Different reward types (cash, points, vouchers)

### ✅ **Easy to Understand & Maintain**

- **Frontend developers** can understand it immediately
- **Backend developers** can modify it easily
- **No complex rule evaluation logic**
- **Clear, predictable behavior**

### ✅ **Still Flexible**

- Can add new event types easily
- Can add new reward types easily
- Can add more limits if needed later
- Can extend without breaking existing functionality

## 🚀 **Migration Strategy**

1. **Create new simplified tables**
2. **Migrate existing reward data** to new format
3. **Replace complex services** with simple service
4. **Update trigger points** in existing code
5. **Remove old complex system**

Would you like me to implement this simplified approach? It will be much cleaner and easier to work with while still covering all your requirements!
