import { db } from '../../utils/model';
import { formatString } from '../../utils/stringFormatter';
import { HttpError } from '../../utils/httpError';
import { logger } from '../../utils/logger';

export const userService = {
  checkUser: async (accountId: any, query: any) => {
    try {
      const email = query.email;
      const formattedEmail = formatString.formatEmail(email);
      let userExist = await db.user.findUnique({
        where: { emailAddress: formattedEmail },
      });
      if (!userExist) {
        throw new HttpError('Patient not found', 400);
      }
      return userExist;
    } catch (error) {
      logger.error('Error checking user:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to check user', 400);
    }
  },

  createOrUpdateUser: async (reqBody: any) => {
    try {
      const {
        emailAddress,
        firstName,
        lastName,
        dateOfBirth,
        phoneNumber,
        gender,
      } = reqBody;
      const formattedEmail = formatString.formatEmail(emailAddress);
      let userExist = await db.user.findFirst({
        where: { emailAddress: formattedEmail },
      });
      if (!userExist) {
        userExist = await db.user.create({
          data: {
            emailAddress: formattedEmail,
            firstName,
            lastName,
            dateOfBirth,
            phoneNumber,
            gender,
            account: {
              create: {
                type: 'USER',
              },
            },
          },
        });
      } else if (
        !userExist.firstName ||
        !userExist.lastName ||
        !userExist.dateOfBirth ||
        !userExist.phoneNumber ||
        !userExist.gender
      ) {
        userExist = await db.user.update({
          where: { id: userExist.id },
          data: {
            firstName: firstName || userExist.firstName,
            lastName: lastName || userExist.lastName,
            dateOfBirth: dateOfBirth || userExist.dateOfBirth,
            phoneNumber: phoneNumber || userExist.phoneNumber,
            gender: gender || userExist.gender,
          },
        });
      }
      return userExist;
    } catch (error) {
      logger.error('Error creating or updating user:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to create or update user', 400);
    }
  },

  checkAndCreateUser: async (reqBody: any) => {
    try {
      const { uhid, phone, emailAddress, ...rest } = reqBody;
      const formattedEmail = formatString.formatEmail(emailAddress);
      let userExist = await db.user.findFirst({
        where: {
          OR: [
            { uhid: uhid },
            { phoneNumber: phone },
            { emailAddress: formattedEmail },
          ],
        },
      });
      if (!userExist) {
        userExist = await db.user.create({
          data: {
            emailAddress: formattedEmail,
            uhid: uhid,
            phoneNumber: phone,
            ...rest,
            account: {
              create: {
                type: 'USER',
              },
            },
          },
        });
      }
      return userExist;
    } catch (error) {
      logger.error('Error checking and creating user:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to check and create user', 400);
    }
  },

  checkAndRewardReferral: async (code: any) => {
    try {
      const referralCode = await db.referralCode.findUnique({
        where: { code: code },
      });
      if (!referralCode) {
        return;
      }
      return referralCode;
    } catch (error) {
      logger.error('Error checking and rewarding referral:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to check and reward referral', 400);
    }
  },
};
