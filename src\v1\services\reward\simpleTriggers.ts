import { RewardEventTypeNew } from '@prisma/client';
import { simpleRewardService } from './simpleReward';
import { logger } from '../../utils/logger';
import { db } from '../../utils/model';

/**
 * Simple Reward Triggers
 * Direct, easy-to-understand functions that trigger rewards for specific events
 * No complex rule evaluation - just simple, predictable reward triggering
 */
export const simpleTriggers = {
  /**
   * Trigger reward when an innovation idea is posted
   * Usage: await simpleTriggers.onInnovationIdeaPosted(staffId, ideaId);
   */
  onInnovationIdeaPosted: async (staffId: number, ideaId: number) => {
    try {
      const results = await simpleRewardService.triggerReward(
        RewardEventTypeNew.INNOVATION_IDEA_POSTED,
        staffId,
        'innovation_idea',
        ideaId
      );

      if (results.length > 0) {
        logger.info(
          `✅ Rewarded staff ${staffId} for posting innovation idea ${ideaId} - ${results.length} rewards given`
        );
      }

      return results;
    } catch (error) {
      logger.error(
        `❌ Error triggering innovation idea reward for staff ${staffId}:`,
        error
      );
      return [];
    }
  },

  /**
   * Trigger reward when an innovation idea is accepted
   * Usage: await simpleTriggers.onInnovationIdeaAccepted(staffId, ideaId);
   */
  onInnovationIdeaAccepted: async (staffId: number, ideaId: number) => {
    try {
      const results = await simpleRewardService.triggerReward(
        RewardEventTypeNew.INNOVATION_IDEA_ACCEPTED,
        staffId,
        'innovation_idea_accepted',
        ideaId
      );

      if (results.length > 0) {
        logger.info(
          `✅ Rewarded staff ${staffId} for innovation idea ${ideaId} acceptance - ${results.length} rewards given`
        );
      }

      return results;
    } catch (error) {
      logger.error(
        `❌ Error triggering innovation idea acceptance reward for staff ${staffId}:`,
        error
      );
      return [];
    }
  },

  /**
   * Trigger reward when a comment is posted on an innovation idea
   * Usage: await simpleTriggers.onInnovationComment(staffId, commentId, ideaId);
   */
  onInnovationComment: async (
    staffId: number,
    commentId: number,
    ideaId: number
  ) => {
    try {
      const results = await simpleRewardService.triggerReward(
        RewardEventTypeNew.INNOVATION_IDEA_COMMENT,
        staffId,
        'innovation_idea',
        ideaId
      );

      if (results.length > 0) {
        logger.info(
          `✅ Rewarded staff ${staffId} for commenting on innovation idea ${ideaId} - ${results.length} rewards given`
        );
      }

      return results;
    } catch (error) {
      logger.error(
        `❌ Error triggering innovation comment reward for staff ${staffId}:`,
        error
      );
      return [];
    }
  },

  /**
   * Trigger reward when an innovation idea is liked
   * Usage: await simpleTriggers.onInnovationLike(staffId, likeId, ideaId);
   * Note: For your use case, this should also handle removing rewards when disliked
   */
  onInnovationLike: async (
    staffId: number,
    likeId: number,
    ideaId?: number
  ) => {
    try {
      const results = await simpleRewardService.triggerReward(
        RewardEventTypeNew.INNOVATION_IDEA_LIKE,
        staffId,
        'innovation_like',
        likeId
      );

      if (results.length > 0) {
        logger.info(
          `✅ Rewarded staff ${staffId} for liking innovation idea ${ideaId} - ${results.length} rewards given`
        );
      }

      return results;
    } catch (error) {
      logger.error(
        `❌ Error triggering innovation like reward for staff ${staffId}:`,
        error
      );
      return [];
    }
  },

  /**
   * Remove reward when someone unlikes/dislikes an innovation idea
   * Usage: await simpleTriggers.onInnovationUnlike(staffId, likeId, ideaId);
   */
  onInnovationUnlike: async (
    staffId: number,
    likeId: number,
    ideaId: number
  ) => {
    try {
      // Find and remove the reward event log for this specific like
      const rewardLog = await db.rewardEventLog.findFirst({
        where: {
          staffId,
          entityType: 'innovation_like',
          entityId: likeId,
          eventType: RewardEventTypeNew.INNOVATION_IDEA_LIKE,
        },
        include: { reward: true },
      });

      if (rewardLog) {
        // Remove the reward log
        await db.rewardEventLog.delete({
          where: { id: rewardLog.id },
        });

        // If it was a cash reward, reverse the transaction
        if (rewardLog.valueType === 'POINTS') {
          await db.staff.update({
            where: { id: staffId },
            data: { pointsAccrued: { decrement: Number(rewardLog.value) } },
          });
        }

        logger.info(
          `✅ Removed reward for staff ${staffId} for unliking innovation idea ${ideaId}`
        );
        return [rewardLog];
      }

      return [];
    } catch (error) {
      logger.error('Error removing innovation like reward:', error);
      return [];
    }
  },

  /**
   * Trigger reward when a package is booked/confirmed
   * Usage: await simpleTriggers.onPackageConfirmed(staffId, bookingId, packageId, amount);
   */
  onPackageOnsiteBooking: async (
    staffId: number,
    bookingId: string,
    amount?: number
  ) => {
    try {
      logger.debug(
        `Triggering package confirmation reward for staff ${staffId}, booking ${bookingId}`
      );

      const results = await simpleRewardService.triggerReward(
        RewardEventTypeNew.PACKAGE_REFERRAL_ONSITE,
        staffId,
        'package_booking',
        bookingId,
        amount
      );

      if (results.length > 0) {
        logger.info(
          `✅ Rewarded staff ${staffId} for package booking ${bookingId} - ${results.length} rewards given`
        );
      }

      return results;
    } catch (error) {
      logger.error(
        `❌ Error triggering package confirmation reward for staff ${staffId}:`,
        error
      );
      return [];
    }
  },

  /**
   * Trigger reward when someone uses a referral code (referrer gets rewarded)
   * Usage: await simpleTriggers.onPackageReferral(referrerStaffId, bookingId, referralCodeId);
   */
  onPackageOnlineBooking: async (
    referrerStaffId: number,
    bookingId: string,
    amount?: number
  ) => {
    try {
      logger.debug(
        `Triggering package referral reward for staff ${referrerStaffId}, booking ${bookingId}`
      );

      const results = await simpleRewardService.triggerReward(
        RewardEventTypeNew.PACKAGE_REFERRAL_ONLINE,
        referrerStaffId,
        'package_referral',
        bookingId,
        amount
      );

      if (results.length > 0) {
        logger.info(
          `✅ Rewarded staff ${referrerStaffId} for referral leading to booking ${bookingId} - ${results.length} rewards given`
        );
      }

      return results;
    } catch (error) {
      logger.error(
        `❌ Error triggering package referral reward for staff ${referrerStaffId}:`,
        error
      );
      return [];
    }
  },

  /**
   * Trigger reward for game completion
   * Usage: await simpleTriggers.onGameCompletion(staffId, gameId, position, score);
   */
  // onGameCompletion: async (
  //   staffId: number,
  //   gameId: string,
  //   position?: number,
  //   score?: number
  // ) => {
  //   try {
  //     logger.debug(
  //       `Triggering game completion reward for staff ${staffId}, game ${gameId}`
  //     );

  //     const results = await simpleRewardService.triggerReward(
  //       RewardEventType.GAME_COMPLETION,
  //       staffId,
  //       'game_completion',
  //       parseInt(gameId) // Convert string to number for consistency
  //     );

  //     if (results.length > 0) {
  //       logger.info(
  //         `✅ Rewarded staff ${staffId} for completing game ${gameId} - ${results.length} rewards given`
  //       );
  //     }

  //     return results;
  //   } catch (error) {
  //     logger.error(
  //       `❌ Error triggering game completion reward for staff ${staffId}:`,
  //       error
  //     );
  //     return [];
  //   }
  // },

  /**
   * Batch trigger multiple rewards for a staff member
   * Useful for complex scenarios where multiple events happen at once
   */
  triggerMultiple: async (
    triggers: Array<{
      eventType: RewardEventTypeNew;
      staffId: number;
      entityType?: string;
      entityId?: number;
    }>
  ) => {
    try {
      const allResults = [];

      for (const trigger of triggers) {
        const results = await simpleRewardService.triggerReward(
          trigger.eventType,
          trigger.staffId,
          trigger.entityType,
          trigger.entityId
        );
        allResults.push(...results);
      }

      if (allResults.length > 0) {
        logger.info(
          `✅ Batch triggered ${allResults.length} rewards for ${triggers.length} events`
        );
      }

      return allResults;
    } catch (error) {
      logger.error('❌ Error in batch trigger:', error);
      return [];
    }
  },
};
