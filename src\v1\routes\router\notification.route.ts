import { Router } from 'express';
import { secure } from '../../middleware/auth';
import { notificationControllers } from '../../controllers/notification/notification.controller';

export const notificationRoute = Router();

// Get user notifications with pagination and filters
// GET /notifications?page=1&limit=20&type=forum_message&priority=high&isRead=false&startDate=2024-01-01&endDate=2024-12-31
notificationRoute.get(
  '/list',
  secure,
  notificationControllers.GetNotificationsHandler
);

// Get unread notification count
// GET /notifications/unread-count
notificationRoute.get(
  '/unread-count',
  secure,
  notificationControllers.GetUnreadCountHandler
);

// Get notification statistics
// GET /notifications/stats
notificationRoute.get(
  '/stats',
  secure,
  notificationControllers.GetStatsHandler
);

// Get recent activity for dashboard
// GET /notifications/recent?limit=10
notificationRoute.get(
  '/recent',
  secure,
  notificationControllers.GetRecentActivityHandler
);

// Get notification preferences
// GET /notifications/preferences
notificationRoute.get(
  '/preferences',
  secure,
  notificationControllers.GetPreferencesHandler
);

// Update notification preferences
// PUT /notifications/preferences
notificationRoute.patch(
  '/preferences',
  secure,
  notificationControllers.UpdatePreferencesHandler
);

// Mark specific notifications as read
// PUT /notifications/mark-read
notificationRoute.patch(
  '/:notificationId/mark-read',
  secure,
  notificationControllers.MarkAsReadHandler
);

// Mark all notifications as read
// PUT /notifications/mark-all-read
notificationRoute.patch(
  '/mark-all-read',
  secure,
  notificationControllers.MarkAllAsReadHandler
);

// Delete specific notifications
// DELETE /notifications
notificationRoute.delete(
  '/delete/:notificationId',
  secure,
  notificationControllers.DeleteNotificationsHandler
);

// Delete specific notifications
// DELETE /notifications
notificationRoute.delete(
  '/clear-all',
  secure,
  notificationControllers.DeleteAllNotificationsHandler
);

// Send bulk notification to multiple users (Admin only)
// POST /notifications/bulk
notificationRoute.post(
  '/bulk',
  secure,
  notificationControllers.SendBulkNotificationHandler
);

// Clean up old notifications (Admin only)
// DELETE /notifications/cleanup
notificationRoute.delete(
  '/cleanup',
  secure,
  notificationControllers.CleanupOldNotificationsHandler
);
